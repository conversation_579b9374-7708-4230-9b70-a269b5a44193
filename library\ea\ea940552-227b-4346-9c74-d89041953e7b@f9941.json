{"__type__": "cc.SpriteFrame", "content": {"name": "skel_hudun", "atlas": "", "rect": {"x": 1, "y": 1, "width": 1978, "height": 1966}, "offset": {"x": -34, "y": 40}, "originalSize": {"width": 2048, "height": 2048}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-989, -983, 0, 989, -983, 0, -989, 983, 0, 989, 983, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [1, 2047, 1979, 2047, 1, 81, 1979, 81], "nuv": [0.00048828125, 0.03955078125, 0.96630859375, 0.03955078125, 0.00048828125, 0.99951171875, 0.96630859375, 0.99951171875], "minPos": {"x": -989, "y": -983, "z": 0}, "maxPos": {"x": 989, "y": 983, "z": 0}}, "texture": "ea940552-227b-4346-9c74-d89041953e7b@6c48a", "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}}