{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyData.ts"], "names": ["EnemyData", "error", "MyApp", "AttributeData", "AttributeConst", "_planeId", "config", "planeId", "value", "lubanTables", "TbResEnemy", "get", "updateData", "recourseSpine", "setBaseAttribute", "MaxHP", "hp", "Attack", "atk"], "mappings": ";;;4HAMaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;AALJC,MAAAA,K,OAAAA,K;;AADAC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;2BAGIJ,S,GAAN,MAAMA,SAAN;AAAA;AAAA,0CAAsC;AAAA;AAAA;AAAA,eACzCK,QADyC,GACtB,CADsB;AACpB;AADoB,eAGzCC,MAHyC,GAGhB,IAHgB;AAAA;;AAGX;AAEnB,YAAPC,OAAO,GAAE;AACT,iBAAO,KAAKF,QAAZ;AACH;;AAEU,YAAPE,OAAO,CAACC,KAAD,EAAO;AACd,cAAGA,KAAK,IAAE,KAAKH,QAAf,EAAwB;AACpB,iBAAKA,QAAL,GAAgBG,KAAhB;AACA,iBAAKF,MAAL,GAAc;AAAA;AAAA,gCAAMG,WAAN,CAAkBC,UAAlB,CAA6BC,GAA7B,CAAiC,KAAKN,QAAtC,CAAd;AACA,iBAAKO,UAAL;AACH;AACJ;;AAEgB,YAAbC,aAAa,GAAG;AAChB,cAAI,CAAC,KAAKP,MAAV,EAAiB;AACb,mBAAO,EAAP;AACH;AACJ;;AAEDM,QAAAA,UAAU,GAAE;AACR,cAAI,CAAC,KAAKN,MAAV,EAAiB;AACbL,YAAAA,KAAK,CAAC,yDAAD,CAAL;AACA;AACH;;AACD,eAAKa,gBAAL,CAAsB;AAAA;AAAA,gDAAeC,KAArC,EAA2C,KAAKT,MAAL,CAAYU,EAAvD;AACA,eAAKF,gBAAL,CAAsB;AAAA;AAAA,gDAAeG,MAArC,EAA4C,KAAKX,MAAL,CAAYY,GAAxD;AACH;;AA9BwC,O", "sourcesContent": ["import { MyApp } from 'db://assets/bundles/common/script/app/MyApp';\r\nimport { error } from \"cc\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { ResEnemy } from \"../../autogen/luban/schema\";\r\n\r\nexport class EnemyData extends AttributeData {\r\n    _planeId: number = 0;//飞机id\r\n    \r\n    config:ResEnemy | null = null;//飞机静态配置\r\n    \r\n    get planeId(){\r\n        return this._planeId;\r\n    }\r\n\r\n    set planeId(value){\r\n        if(value!=this._planeId){\r\n            this._planeId = value;\r\n            this.config = MyApp.lubanTables.TbResEnemy.get(this._planeId)!;\r\n            this.updateData();\r\n        }\r\n    }\r\n\r\n    get recourseSpine() {\r\n        if (!this.config){\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    updateData(){\r\n        if (!this.config){\r\n            error(\"enemyPlane lv config is null, cannot update attributes.\");\r\n            return;\r\n        }\r\n        this.setBaseAttribute(AttributeConst.MaxHP,this.config.hp);\r\n        this.setBaseAttribute(AttributeConst.Attack,this.config.atk);\r\n    }\r\n}"]}