{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/bullet/ExpressionValue.ts"], "names": ["_decorator", "RPNCalculator", "SerializableRPNProgram", "ccclass", "property", "ExpressionValue", "visible", "type", "displayName", "raw", "isExpression", "expression", "value", "toString", "replace", "ch", "String", "fromCharCode", "charCodeAt", "test", "parsed", "parseFloat", "isNaN", "program", "calc", "compile", "serializedProgram", "fromRPNProgram", "eval", "context", "isCompiled", "toRPNProgram", "registry", "result", "evaluate", "getDebugInfo", "variables", "varNames", "codeLength", "code", "length", "constantsCount", "consts", "constructor", "str"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;;AADFC,MAAAA,a;AAAyCC,MAAAA,sB,iBAAAA,sB;;;;;;;;;OAE1C;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U;;iCAGjBK,e,WADZF,OAAO,CAAC,iBAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAERF,QAAQ,CAAC;AAACE,QAAAA,OAAO,EAAE;AAAV,OAAD,C,UAIRF,QAAQ,CAAC;AAACG,QAAAA,IAAI;AAAA;AAAA,4DAAL;AAA+BD,QAAAA,OAAO,EAAE;AAAxC,OAAD,C,UAQRF,QAAQ,CAAC;AAACI,QAAAA,WAAW,EAAE;AAAd,OAAD,C,sCAlBb,MACaH,eADb,CAC6B;AAkBX,YAAHI,GAAG,GAAW;AACrB,iBAAO,KAAKC,YAAL,GAAoB,KAAKC,UAAzB,GAAsC,KAAKC,KAAL,CAAWC,QAAX,EAA7C;AACH;;AACa,YAAHJ,GAAG,CAACG,KAAD,EAAgB;AAC1B;AACA,eAAKD,UAAL,GAAkBC,KAAK,CAACE,OAAN,CAAc,kBAAd,EAAmCC,EAAD,IAAQC,MAAM,CAACC,YAAP,CAAoBF,EAAE,CAACG,UAAH,CAAc,CAAd,IAAmB,MAAvC,CAA1C,CAAlB,CAF0B,CAG1B;;AACA,eAAKR,YAAL,GAAoB,WAAWS,IAAX,CAAgBP,KAAhB,CAApB;;AACA,cAAI,CAAC,KAAKF,YAAV,EAAwB;AACpB;AACA,gBAAMU,MAAM,GAAGC,UAAU,CAACT,KAAD,CAAzB;;AACA,gBAAI,CAACU,KAAK,CAACF,MAAD,CAAV,EAAoB;AAChB,mBAAKR,KAAL,GAAaQ,MAAb;AACH;AACJ,WAND,MAOK;AACD;AACA,iBAAKG,OAAL,GAAelB,eAAe,CAACmB,IAAhB,CAAqBC,OAArB,CAA6B,KAAKd,UAAlC,CAAf;AACA,iBAAKe,iBAAL,GAAyB;AAAA;AAAA,kEAAuBC,cAAvB,CAAsC,KAAKJ,OAA3C,CAAzB;AACH;AACJ;;AAEMK,QAAAA,IAAI,CAACC,OAAD,EAA0C;AAAA,cAAzCA,OAAyC;AAAzCA,YAAAA,OAAyC,GAAd,IAAc;AAAA;;AACjD,cAAI,KAAKnB,YAAT,EAAuB;AAAA;;AACnB,gBAAI,CAAC,KAAKa,OAAN,6BAAiB,KAAKG,iBAAtB,aAAiB,sBAAwBI,UAAxB,EAArB,EAA2D;AACvD,mBAAKP,OAAL,GAAe,KAAKG,iBAAL,CAAuBK,YAAvB,CAAoC1B,eAAe,CAACmB,IAAhB,CAAqBQ,QAAzD,CAAf;AACH;;AAED,gBAAI,KAAKT,OAAT,EAAkB;AACd,kBAAMU,MAAM,GAAG,KAAKV,OAAL,CAAaW,QAAb,CAAsBL,OAAO,IAAI,EAAjC,CAAf;AACA,qBAAO,OAAOI,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAuCA,MAAM,GAAG,CAAH,GAAO,CAA3D,CAFc,CAEiD;AAClE;;AACD,mBAAO,KAAKrB,KAAZ;AACH;;AACD,iBAAO,KAAKA,KAAZ;AACH,SArDwB,CAuDzB;;;AACOuB,QAAAA,YAAY,GAAQ;AAAA;;AACvB,cAAI,CAAC,KAAKzB,YAAV,EAAwB;AACpB,mBAAO;AAAEH,cAAAA,IAAI,EAAE,QAAR;AAAkBK,cAAAA,KAAK,EAAE,KAAKA;AAA9B,aAAP;AACH;;AAED,iBAAO;AACHL,YAAAA,IAAI,EAAE,YADH;AAEHI,YAAAA,UAAU,EAAE,KAAKA,UAFd;AAGHmB,YAAAA,UAAU,4BAAE,KAAKJ,iBAAP,qBAAE,uBAAwBI,UAAxB,EAHT;AAIHM,YAAAA,SAAS,4BAAE,KAAKV,iBAAP,qBAAE,uBAAwBW,QAJhC;AAKHC,YAAAA,UAAU,4BAAE,KAAKZ,iBAAP,qBAAE,uBAAwBa,IAAxB,CAA6BC,MALtC;AAMHC,YAAAA,cAAc,4BAAE,KAAKf,iBAAP,qBAAE,uBAAwBgB,MAAxB,CAA+BF;AAN5C,WAAP;AAQH;;AAEDG,QAAAA,WAAW,CAACC,GAAD,EAAe;AAAA;;AAAA;;AAAA;;AA/D1B;AA+D0B;;AA3D1B;AA2D0B,eA1DlBrB,OA0DkB,GA1DW,IA0DX;AACtB,eAAKd,GAAL,GAAWmC,GAAG,IAAI,EAAlB;AACH;;AAzEwB,O,UAelBpB,I,GAAO;AAAA;AAAA,2C;;;;;iBAbS,C;;;;;;;iBAEQ,K;;;;;;;iBAEH,E;;;;;;;iBAI+B,I", "sourcesContent": ["import RPNCalculator, { R<PERSON><PERSON><PERSON>ram, RPNContext, SerializableRPNProgram } from \"db://assets/bundles/common/script/game/utils/RPN\";\r\nimport { _decorator, Enum } from \"cc\";\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('ExpressionValue')\r\nexport class ExpressionValue {\r\n    @property({visible: false})\r\n    public value: number = 0;\r\n    @property({visible: false})\r\n    public isExpression: boolean = false;\r\n    @property({visible: false})\r\n    public expression: string = '';\r\n\r\n    // Serializable compiled program for editor storage\r\n    @property({type: SerializableRPNProgram, visible: false})\r\n    private serializedProgram: SerializableRPNProgram | null = null;\r\n\r\n    // Runtime program (not serialized)\r\n    private program: RPNProgram | null = null;\r\n\r\n    static calc = new RPNCalculator();\r\n    \r\n    @property({displayName: '值'})\r\n    public get raw(): string {\r\n        return this.isExpression ? this.expression : this.value.toString();\r\n    }\r\n    public set raw(value: string) {\r\n        // 替换全角为半角\r\n        this.expression = value.replace(/[\\uff01-\\uff5e]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xfee0));\r\n        // if includes any 'letters' then it is a expression\r\n        this.isExpression = /[a-zA-Z]/.test(value);\r\n        if (!this.isExpression) {\r\n            // try parse number\r\n            const parsed = parseFloat(value);\r\n            if (!isNaN(parsed)) {\r\n                this.value = parsed;\r\n            }\r\n        }\r\n        else {\r\n            // Compile and store both runtime and serializable versions\r\n            this.program = ExpressionValue.calc.compile(this.expression);\r\n            this.serializedProgram = SerializableRPNProgram.fromRPNProgram(this.program);\r\n        }\r\n    }\r\n\r\n    public eval(context: RPNContext|null = null): number {\r\n        if (this.isExpression) {\r\n            if (!this.program && this.serializedProgram?.isCompiled()) {\r\n                this.program = this.serializedProgram.toRPNProgram(ExpressionValue.calc.registry);\r\n            }\r\n\r\n            if (this.program) {\r\n                const result = this.program.evaluate(context || {});\r\n                return typeof result === 'number' ? result : (result ? 1 : 0); // return 0 if false else 1\r\n            }\r\n            return this.value;\r\n        }\r\n        return this.value;\r\n    }\r\n\r\n    // Get debug info about the compiled program\r\n    public getDebugInfo(): any {\r\n        if (!this.isExpression) {\r\n            return { type: 'number', value: this.value };\r\n        }\r\n\r\n        return {\r\n            type: 'expression',\r\n            expression: this.expression,\r\n            isCompiled: this.serializedProgram?.isCompiled(),\r\n            variables: this.serializedProgram?.varNames,\r\n            codeLength: this.serializedProgram?.code.length,\r\n            constantsCount: this.serializedProgram?.consts.length\r\n        };\r\n    }\r\n\r\n    constructor(str?: string) {\r\n        this.raw = str || '';\r\n    }\r\n}"]}