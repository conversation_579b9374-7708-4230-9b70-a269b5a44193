{"__type__": "sp.SkeletonData", "_name": "skel_shield_skill_front", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_skeletonJson": {"skeleton": {"hash": "+ZSf5QyW/eMskeg1fxwdEoqk8Os", "spine": "3.6.53", "width": 327.06, "height": 361.84, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "Dun", "parent": "root", "scaleX": 0.9, "scaleY": 0.9, "color": "7e00ffff"}, {"name": "Dun0", "parent": "Dun", "color": "ca03b3ff"}, {"name": "Dun1", "parent": "Dun", "length": 102.12, "rotation": -159.21, "x": 151.1, "y": 63, "color": "7e00ffff"}, {"name": "Dun3", "parent": "Dun", "y": 79.19, "color": "7e00ffff"}, {"name": "Dun4", "parent": "Dun", "length": 59.33, "rotation": 156.8, "x": 104.26, "y": -100.5, "color": "7e00ffff"}, {"name": "Dun5", "parent": "Dun", "x": -4.55, "y": 155.77, "color": "7e00ffff"}, {"name": "Dun6", "parent": "Dun", "length": 62.92, "rotation": 24.04, "x": -103.37, "y": -104.16, "color": "7e00ffff"}, {"name": "Dun7", "parent": "Dun", "x": -1.28, "y": -83.24, "color": "7e00ffff"}, {"name": "Dun8", "parent": "Dun", "length": 91.77, "rotation": -20.88, "x": -148.24, "y": 61.87, "color": "7e00ffff"}, {"name": "Dun1+", "parent": "Dun1", "rotation": 159.21, "x": 50.64, "y": -7.49, "scaleX": 2, "scaleY": 2, "color": "ff5c00ff"}, {"name": "Dun2+", "parent": "Dun4", "rotation": -156.8, "x": 30.32, "y": -0.63, "scaleX": 2, "scaleY": 2, "color": "ff5c00ff"}, {"name": "Dun3+", "parent": "Dun8", "rotation": 20.88, "x": 45.9, "y": 8.59, "scaleX": 2, "scaleY": 2, "color": "ff5c00ff"}, {"name": "Dun4+", "parent": "Dun6", "rotation": -24.04, "x": 31.4, "y": 2.06, "scaleX": 2, "scaleY": 2, "color": "ff5300ff"}, {"name": "Dun5+", "parent": "Dun5", "y": 0.03, "scaleX": 2, "scaleY": 2, "color": "ff5300ff"}, {"name": "Dun6+", "parent": "Dun3", "y": -1.8, "scaleX": 2, "scaleY": 2, "color": "ffad0000"}, {"name": "Dun7+", "parent": "Dun7", "scaleX": 2, "scaleY": 2, "color": "ff5c00ff"}, {"name": "T-di", "parent": "root", "color": "42ff00ff"}, {"name": "wen", "parent": "Dun", "color": "00ffe1ff"}], "slots": [{"name": "T-di", "bone": "T-di", "attachment": "T-di", "blend": "additive"}, {"name": "Dun1", "bone": "Dun1", "attachment": "Dun1"}, {"name": "Dun2", "bone": "Dun4", "attachment": "Dun2"}, {"name": "Dun3", "bone": "Dun8", "attachment": "Dun3"}, {"name": "Dun4", "bone": "Dun6", "attachment": "Dun4"}, {"name": "Dun5", "bone": "Dun5", "attachment": "Dun5"}, {"name": "Dun6", "bone": "Dun3", "attachment": "Dun6"}, {"name": "Dun7", "bone": "Dun7", "attachment": "Dun7"}, {"name": "dun", "bone": "Dun0", "attachment": "dun", "blend": "additive"}, {"name": "wen", "bone": "wen", "attachment": "wen", "blend": "additive"}, {"name": "Dun6+", "bone": "Dun6+", "attachment": "Dun6+", "blend": "additive"}, {"name": "Dun3+", "bone": "Dun3+", "attachment": "Dun3+", "blend": "additive"}, {"name": "Dun4+", "bone": "Dun4+", "attachment": "Dun4+", "blend": "additive"}, {"name": "Dun5+", "bone": "Dun5+", "attachment": "Dun5+", "blend": "additive"}, {"name": "Dun7+", "bone": "Dun7+", "attachment": "Dun7+", "blend": "additive"}, {"name": "Dun1+", "bone": "Dun1+", "attachment": "Dun1+", "blend": "additive"}, {"name": "Dun2+", "bone": "Dun2+", "attachment": "Dun2+", "blend": "additive"}], "skins": {"default": {"Dun1": {"Dun1": {"x": 50.64, "y": -7.48, "rotation": 159.21, "width": 121, "height": 206}}, "Dun1+": {"Dun1+": {"width": 80, "height": 119}}, "Dun2": {"Dun2": {"x": 30.33, "y": -0.63, "rotation": -156.8, "width": 110, "height": 175}}, "Dun2+": {"Dun2+": {"width": 68, "height": 104}}, "Dun3": {"Dun3": {"x": 45.89, "y": 8.59, "rotation": 20.88, "width": 121, "height": 206}}, "Dun3+": {"Dun3+": {"width": 80, "height": 119}}, "Dun4": {"Dun4": {"x": 31.4, "y": 2.06, "rotation": -24.04, "width": 110, "height": 175}}, "Dun4+": {"Dun4+": {"width": 68, "height": 104}}, "Dun5": {"Dun5": {"width": 166, "height": 51}}, "Dun5+": {"Dun5+": {"x": -0.36, "width": 91, "height": 44}}, "Dun6": {"Dun6": {"width": 138, "height": 126}}, "Dun6+": {"Dun6+": {"width": 87, "height": 83}}, "Dun7": {"Dun7": {"width": 84, "height": 198}}, "Dun7+": {"Dun7+": {"width": 57, "height": 119}}, "T-di": {"T-di": {"width": 312, "height": 217}}, "dun": {"dun": {"width": 161, "height": 182}}, "wen": {"wen": {"width": 180, "height": 202}}}}, "animations": {"finish": {"slots": {"Dun1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun7": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "Dun1+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun1+"}]}, "Dun2+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun2+"}]}, "Dun3+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun3+"}]}, "Dun4+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun4+"}]}, "Dun5+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun5+"}]}, "Dun6+": {"color": [{"time": 0, "color": "ff500000"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffe90000"}], "attachment": [{"time": 0, "name": "Dun6+"}]}, "Dun7+": {"color": [{"time": 0, "color": "ff5100ff", "curve": "stepped"}, {"time": 0.2333, "color": "ff5100ff"}, {"time": 0.4333, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun7+"}]}, "T-di": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "T-di"}]}, "dun": {"color": [{"time": 0, "color": "f4ff2700"}], "attachment": [{"time": 0, "name": "dun"}]}, "wen": {"color": [{"time": 0, "color": "ff5f1700"}], "attachment": [{"time": 0, "name": "wen"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "Dun8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": -41.61, "y": 8.76}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun7": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": 0, "y": -39.42}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": -56.94, "y": -21.9}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": 0, "y": 28.47}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": 54.75, "y": -30.66}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": 39.42, "y": 10.95}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun0": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 4.524, "y": 4.524}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "wen": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 5, "y": 5}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun6+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "T-di": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 8.701, "y": 8.701}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 3.07, "y": 0}], "scale": [{"time": 0, "x": 1.364, "y": 1.364}, {"time": 0.1667, "x": 1.437, "y": 1.437}, {"time": 0.4333, "x": 3.521, "y": 3.521}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun3+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun7+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun4+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun5+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun2+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun1+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}, "idle": {"slots": {"Dun1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun7": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}]}, "Dun1+": {"color": [{"time": 0, "color": "ff5100ff"}], "attachment": [{"time": 0, "name": "Dun1+"}]}, "Dun2+": {"color": [{"time": 0, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun2+"}]}, "Dun3+": {"color": [{"time": 0, "color": "ff5200ff"}], "attachment": [{"time": 0, "name": "Dun3+"}]}, "Dun4+": {"color": [{"time": 0, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun4+"}]}, "Dun5+": {"color": [{"time": 0, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun5+"}]}, "Dun6+": {"color": [{"time": 0, "color": "ffad0000"}]}, "Dun7+": {"color": [{"time": 0, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun7+"}]}, "T-di": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "T-di"}, {"time": 0.6667, "name": "T-di"}]}, "dun": {"color": [{"time": 0, "color": "f4ff2700", "curve": "stepped"}, {"time": 0.6667, "color": "f4ff2700"}], "attachment": [{"time": 0, "name": "dun"}, {"time": 0.6667, "name": "dun"}]}, "wen": {"color": [{"time": 0, "color": "ff5f1700", "curve": "stepped"}, {"time": 0.6667, "color": "ff5f1700"}], "attachment": [{"time": 0, "name": "wen"}, {"time": 0.6667, "name": "wen"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}]}, "Dun8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -8.11, "y": 2.38}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": -4.75, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 2.38}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 4.75, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 9.33, "y": 2.38}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun0": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 4.524, "y": 4.524, "curve": "stepped"}, {"time": 0.6667, "x": 4.524, "y": 4.524}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "wen": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 5, "y": 5, "curve": "stepped"}, {"time": 0.6667, "x": 5, "y": 5}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun6+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "T-di": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 8.701, "y": 8.701, "curve": "stepped"}, {"time": 0.6667, "x": 8.701, "y": 8.701}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}], "translate": [{"time": 0, "x": 3.07, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 3.07, "y": 0}], "scale": [{"time": 0, "x": 1.364, "y": 1.364}, {"time": 0.2667, "x": 1.437, "y": 1.437}, {"time": 0.6667, "x": 1.364, "y": 1.364}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "Dun3+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun7+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun4+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun5+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun2+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun1+": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}, "play": {"slots": {"Dun1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}]}, "Dun2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}]}, "Dun3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}]}, "Dun4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}]}, "Dun5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}]}, "Dun6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "fcc624ff", "curve": "stepped"}, {"time": 1.6333, "color": "fcc624ff"}, {"time": 1.8333, "color": "fccf48ff"}, {"time": 2, "color": "ffffffff"}]}, "Dun7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}]}, "Dun1+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun1+"}]}, "Dun2+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun2+"}]}, "Dun3+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun3+"}]}, "Dun4+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun4+"}]}, "Dun5+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ff5300ff"}], "attachment": [{"time": 0, "name": "Dun5+"}]}, "Dun6+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ff5300ff", "curve": "stepped"}, {"time": 2.1, "color": "ff5300ff"}, {"time": 2.1667, "color": "ffad0000"}], "attachment": [{"time": 0, "name": "Dun6+"}]}, "Dun7+": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6, "color": "ff5300fa"}], "attachment": [{"time": 0, "name": "Dun7+"}]}, "T-di": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "T-di"}]}, "dun": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0333, "color": "ffffffff"}, {"time": 2.2667, "color": "ff592400"}], "attachment": [{"time": 0, "name": "dun"}]}, "wen": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ff0000ff"}, {"time": 2.2667, "color": "ff661600"}], "attachment": [{"time": 0, "name": "wen"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 3.81, "y": -19.06}, {"time": 2.1, "x": 0, "y": 19.07}, {"time": 2.1333, "x": 0, "y": -11.44}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "Dun8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": -121.63, "y": -475.79}, {"time": 1.3333, "x": -191.24, "y": 321.86}, {"time": 2, "x": -172.84, "y": 260}, {"time": 2.1, "x": 5.48, "y": -1.37}, {"time": 2.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 2, "y": 2}, {"time": 1.3333, "x": 1.231, "y": 1.231}, {"time": 2.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": -1133.83}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 2, "y": 2}, {"time": 1.8333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": -691.19, "y": -392.64}, {"time": 1.5, "x": -228.48, "y": -210.88}, {"time": 1.8, "x": -200.94, "y": -115.37}, {"time": 1.8667, "x": 5.48, "y": 1.37}, {"time": 1.9333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 2, "y": 2}, {"time": 1.5, "x": 1.296, "y": 1.296, "curve": "stepped"}, {"time": 1.8, "x": 1.296, "y": 1.296}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.0667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 938.86}, {"time": 2.0667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 4.488, "y": 4.488}, {"time": 2.0667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 649, "y": -405.63}, {"time": 1.5, "x": 189.4, "y": -120.19}, {"time": 1.8, "x": 195.4, "y": -96.19}, {"time": 1.8667, "x": -4.11, "y": 0}, {"time": 1.9333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 2, "y": 2}, {"time": 1.5, "x": 1.296, "y": 1.296, "curve": "stepped"}, {"time": 1.8, "x": 1.296, "y": 1.296}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": -8.95, "y": -494.52}, {"time": 1.6333, "x": -8.43, "y": 244.42}, {"time": 1.8333, "x": -8.06, "y": 233.55}, {"time": 1.9667, "x": -6.56, "y": 190.1}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 2.126, "y": 2.126, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 5.528, "y": 5.528, "curve": [0.299, 0, 0.636, 0.36]}, {"time": 1.8333, "x": 5.242, "y": 5.242, "curve": [0.298, 0.2, 0.756, 1]}, {"time": 2.0667, "x": 0.97, "y": 0.97}, {"time": 2.1333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 321.71}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 43.6, "y": -775.2}, {"time": 1.3333, "x": 174.88, "y": 257.79}, {"time": 2, "x": 192.12, "y": 314.45}, {"time": 2.1, "x": -4.11, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 2, "y": 2}, {"time": 1.3333, "x": 1.222, "y": 1.222}, {"time": 2.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun0": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0, "curve": "stepped"}, {"time": 2.0333, "angle": 0, "curve": "stepped"}, {"time": 2.2667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9667, "x": 2, "y": 2, "curve": "stepped"}, {"time": 2.0333, "x": 2, "y": 2}, {"time": 2.2667, "x": 4.524, "y": 4.524}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "wen": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 2, "y": 2, "curve": "stepped"}, {"time": 1.9667, "x": 2, "y": 2}, {"time": 2.1667, "x": 5, "y": 5}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun6+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "T-di": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9, "x": 1, "y": 1}, {"time": 2.1333, "x": 8.701, "y": 8.701}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}, {"time": 2.1, "x": 3.07, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}, {"time": 2.2667, "x": 1.437, "y": 1.437}, {"time": 2.6667, "x": 1.364, "y": 1.364}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun3+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun7+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun4+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun5+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun2+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "Dun1+": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, "textures": [{"__uuid__": "3bbd41ea-a733-4952-b278-a2363ff2d214@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["skel_shield_skill_front.png"], "scale": 1, "_atlasText": "\nskel_shield_skill_front.png\nsize: 600,600\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nDun1\n  rotate: false\n  xy: 182, 175\n  size: 121, 206\n  orig: 121, 206\n  offset: 0, 0\n  index: -1\nDun1+\n  rotate: false\n  xy: 500, 273\n  size: 80, 119\n  orig: 80, 119\n  offset: 0, 0\n  index: -1\nDun2\n  rotate: false\n  xy: 1, 3\n  size: 110, 175\n  orig: 110, 175\n  offset: 0, 0\n  index: -1\nDun2+\n  rotate: false\n  xy: 303, 70\n  size: 68, 104\n  orig: 68, 104\n  offset: 0, 0\n  index: -1\nDun3\n  rotate: false\n  xy: 476, 393\n  size: 121, 206\n  orig: 121, 206\n  offset: 0, 0\n  index: -1\nDun3+\n  rotate: true\n  xy: 389, 136\n  size: 80, 119\n  orig: 80, 119\n  offset: 0, 0\n  index: -1\nDun4\n  rotate: false\n  xy: 389, 217\n  size: 110, 175\n  orig: 110, 175\n  offset: 0, 0\n  index: -1\nDun4+\n  rotate: true\n  xy: 303, 1\n  size: 68, 104\n  orig: 68, 104\n  offset: 0, 0\n  index: -1\nDun5\n  rotate: true\n  xy: 112, 12\n  size: 166, 51\n  orig: 166, 51\n  offset: 0, 0\n  index: -1\nDun5+\n  rotate: false\n  xy: 164, 3\n  size: 91, 44\n  orig: 91, 44\n  offset: 0, 0\n  index: -1\nDun6\n  rotate: false\n  xy: 164, 48\n  size: 138, 126\n  orig: 138, 126\n  offset: 0, 0\n  index: -1\nDun6+\n  rotate: false\n  xy: 408, 52\n  size: 87, 83\n  orig: 87, 83\n  offset: 0, 0\n  index: -1\nDun7\n  rotate: false\n  xy: 304, 183\n  size: 84, 198\n  orig: 84, 198\n  offset: 0, 0\n  index: -1\nDun7+\n  rotate: false\n  xy: 509, 153\n  size: 57, 119\n  orig: 57, 119\n  offset: 0, 0\n  index: -1\nT-di\n  rotate: false\n  xy: 1, 382\n  size: 312, 217\n  orig: 312, 217\n  offset: 0, 0\n  index: -1\ndun\n  rotate: false\n  xy: 314, 417\n  size: 161, 182\n  orig: 161, 182\n  offset: 0, 0\n  index: -1\nwen\n  rotate: false\n  xy: 1, 179\n  size: 180, 202\n  orig: 180, 202\n  offset: 0, 0\n  index: -1\n"}