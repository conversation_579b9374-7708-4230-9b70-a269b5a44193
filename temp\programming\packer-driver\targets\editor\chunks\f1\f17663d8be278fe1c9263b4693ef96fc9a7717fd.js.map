{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/WaveData.ts"], "names": ["_decorator", "Vec2", "Enum", "CCInteger", "ExpressionValue", "eCompareOp", "eConditionOp", "eTargetValueType", "eWrapMode", "eEasing", "ccclass", "property", "eSpawnOrder", "eWaveAngleType", "eWaveConditionType", "eWaveActionType", "WaveConditionData", "type", "displayName", "visible", "targetValueStr", "targetValue", "raw", "value", "And", "Player_Level", "Equal", "WaveActionData", "durationStr", "duration", "transitionDurationStr", "transitionDuration", "Speed", "Absolute", "Once", "Linear", "WaveEventGroupData", "WaveData", "group", "tooltip", "_spawnPos", "planeListStr", "planeList", "join", "split", "map", "item", "parseInt", "spawnPosXStr", "spawnPosX", "spawnPosYStr", "spawnPosY", "spawnPos", "set", "eval", "spawnAngleStr", "spawnAngle", "isFixedAngleType", "planeAngleType", "Fixed", "Sequential", "FacingMoveDir"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAuBC,MAAAA,I,OAAAA,I;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,S,OAAAA,S;;AAC3CC,MAAAA,e,iBAAAA,e;;AACqBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,Y,iBAAAA,Y;AAAgCC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,S,iBAAAA,S;;AACnFC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;6BAElBY,W,0BAAAA,W;AAAAA,QAAAA,W,CAAAA,W;AAAAA,QAAAA,W,CAAAA,W;eAAAA,W;;;gCAKAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;oCAIAC,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;iCAMAC,e,0BAAAA,e;AAAAA,QAAAA,e,CAAAA,e;AAAAA,QAAAA,e,CAAAA,e;eAAAA,e;cAKZ;;;mCAEaC,iB,WADZN,OAAO,CAAC,mBAAD,C,UAEHC,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,yCAAZ;AAA4BgB,QAAAA,WAAW,EAAE;AAAzC,OAAD,C,UAGRP,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRR,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,qCAAZ;AAA0BgB,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,UAIRP,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAERR,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BAdb,MACaF,iBADb,CAC8D;AAAA;AAAA;;AAAA;;AAAA;;AAU1D;AAV0D;AAAA;;AAcjC,YAAdI,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACG,KAAD,EAAgB;AAAE,eAAKF,WAAL,CAAiBC,GAAjB,GAAuBC,KAAvB;AAA+B;;AAfhB,O;;;;;iBAEhC;AAAA;AAAA,4CAAaC,G;;;;;;;iBAGLV,kBAAkB,CAACW,Y;;;;;;;iBAGtB;AAAA;AAAA,wCAAWC,K;;;;;;;iBAIH;AAAA;AAAA,kDAAoB,GAApB,C;;;;gCAO9BC,c,YADZjB,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAIRR,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRP,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE;AAAd,OAAD,C,WAIRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,iDAAZ;AAAgCgB,QAAAA,WAAW,EAAE;AAA7C,OAAD,C,WAGRP,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAIRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,mCAAZ;AAAyBgB,QAAAA,WAAW,EAAE;AAAtC,OAAD,C,WAGRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAEf,IAAI;AAAA;AAAA,+BAAZ;AAAuBgB,QAAAA,WAAW,EAAE;AAApC,OAAD,C,6BA9Bb,MACaS,cADb,CACwD;AAAA;AAAA;;AAIpD;AAJoD;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAQ9B,YAAXC,WAAW,GAAW;AAAE,iBAAO,KAAKC,QAAL,CAAcP,GAArB;AAA2B;;AACxC,YAAXM,WAAW,CAACL,KAAD,EAAgB;AAAE,eAAKM,QAAL,CAAcP,GAAd,GAAoBC,KAApB;AAA4B;;AAK3C,YAAdH,cAAc,GAAW;AAAE,iBAAO,KAAKC,WAAL,CAAiBC,GAAxB;AAA8B;;AAC3C,YAAdF,cAAc,CAACG,KAAD,EAAgB;AAAE,eAAKF,WAAL,CAAiBC,GAAjB,GAAuBC,KAAvB;AAA+B;;AAQ1C,YAArBO,qBAAqB,GAAW;AAAE,iBAAO,KAAKC,kBAAL,CAAwBR,KAA/B;AAAuC;;AACpD,YAArBO,qBAAqB,CAACP,KAAD,EAAgB;AAAE,eAAKQ,kBAAL,CAAwBR,KAAxB,GAAgCA,KAAhC;AAAwC;;AAxBtC,O;;;;;iBAErBR,eAAe,CAACiB,K;;;;;;;iBAIZ;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMG;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMK;AAAA;AAAA,oDAAiBC,Q;;;;;;;iBAGd;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMxB;AAAA;AAAA,sCAAUC,I;;;;;;;iBAGP;AAAA;AAAA,kCAAQC,M;;;;oCAIxBC,kB,aADZ1B,OAAO,CAAC,oBAAD,C,WAEHC,QAAQ,CAAC;AAAEO,QAAAA,WAAW,EAAE;AAAf,OAAD,C,WAGRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAE,CAACD,iBAAD,CAAR;AAA6BE,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,WAGRP,QAAQ,CAAC;AAAEM,QAAAA,IAAI,EAAE,CAACU,cAAD,CAAR;AAA0BT,QAAAA,WAAW,EAAE;AAAvC,OAAD,C,8BARb,MACakB,kBADb,CACgC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEN,E;;;;;;;iBAGmB,E;;;;;;;iBAGN,E;;;AAGvC;AACA;AACA;AACA;;;0BAEaC,Q,aADZ3B,OAAO,CAAC,UAAD,C,WAIHC,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAGRR,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE,YAAd;AAA4BoB,QAAAA,KAAK,EAAE;AAAnC,OAAD,C,WAYR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEf,IAAI,CAACU,WAAD,CAAX;AAA0BM,QAAAA,WAAW,EAAE,MAAvC;AAA+CoB,QAAAA,KAAK,EAAE;AAAtD,OAAD,C,WAGR3B,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE,UAAd;AAA0BoB,QAAAA,KAAK,EAAE;AAAjC,OAAD,C,WAGR3B,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE,OAAd;AAAuBoB,QAAAA,KAAK,EAAE;AAA9B,OAAD,C,WAGR3B,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE,OAAd;AAAuBoB,QAAAA,KAAK,EAAE;AAA9B,OAAD,C,WAUR3B,QAAQ,CAAC;AAACQ,QAAAA,OAAO,EAAC;AAAT,OAAD,C,WAERR,QAAQ,CAAC;AAACO,QAAAA,WAAW,EAAE,MAAd;AAAsBoB,QAAAA,KAAK,EAAE;AAA7B,OAAD,C,WAIR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEd,SAAP;AAAkBe,QAAAA,WAAW,EAAE,QAA/B;AAAyCqB,QAAAA,OAAO,EAAE,+BAAlD;AAAmFD,QAAAA,KAAK,EAAE;AAA1F,OAAD,C,WAGR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEd,SAAP;AAAkBe,QAAAA,WAAW,EAAE,MAA/B;AAAuCoB,QAAAA,KAAK,EAAE;AAA9C,OAAD,C,WAGR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEf,IAAI,CAACW,cAAD,CAAX;AAA6BK,QAAAA,WAAW,EAAE,QAA1C;AAAoDoB,QAAAA,KAAK,EAAE;AAA3D,OAAD,C,WAMR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEd,SAAP;AACN;AACA;AACA;AACAe,QAAAA,WAAW,EAAE,MAJP;AAIeqB,QAAAA,OAAO,EAAE,mBAJxB;AAI6CD,QAAAA,KAAK,EAAE;AAJpD,OAAD,C,WAOR3B,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAE,CAACmB,kBAAD,CAAP;AAA6BlB,QAAAA,WAAW,EAAE,KAA1C;AAAiDoB,QAAAA,KAAK,EAAE;AAAxD,OAAD,C,gCAnEb,MACaD,QADb,CACsB;AAAA;AAClB;AACA;AAFkB;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAmCVG,SAnCU,GAmCQ,IAAIvC,IAAJ,EAnCR;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAOK,YAAZwC,YAAY,GAAW;AAC9B,iBAAO,CAAC,KAAKC,SAAL,IAAkB,EAAnB,EAAuBC,IAAvB,CAA4B,GAA5B,CAAP;AACH;;AACsB,YAAZF,YAAY,CAAClB,KAAD,EAAgB;AACnC,cAAIA,KAAK,KAAK,EAAd,EAAkB;AACd,iBAAKmB,SAAL,GAAiB,EAAjB;AACA;AACH;;AACD,eAAKA,SAAL,GAAiBnB,KAAK,CAACqB,KAAN,CAAY,GAAZ,EAAiBC,GAAjB,CAAsBC,IAAD,IAAUC,QAAQ,CAACD,IAAD,CAAvC,CAAjB;AACH;;AAasB,YAAZE,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe3B,GAAtB;AAA4B;;AACzC,YAAZ0B,YAAY,CAACzB,KAAD,EAAgB;AAAE,eAAK0B,SAAL,CAAe3B,GAAf,GAAqBC,KAArB;AAA6B;;AAE/C,YAAZ2B,YAAY,GAAW;AAAE,iBAAO,KAAKC,SAAL,CAAe7B,GAAtB;AAA4B;;AACzC,YAAZ4B,YAAY,CAAC3B,KAAD,EAAgB;AAAE,eAAK4B,SAAL,CAAe7B,GAAf,GAAqBC,KAArB;AAA6B;;AAGnD,YAAR6B,QAAQ,GAAS;AACxB,eAAKZ,SAAL,CAAea,GAAf,CAAmB,KAAKJ,SAAL,CAAeK,IAAf,EAAnB,EAA0C,KAAKH,SAAL,CAAeG,IAAf,EAA1C;;AACA,iBAAO,KAAKd,SAAZ;AACH;;AAKuB,YAAbe,aAAa,GAAW;AAAE,iBAAO,KAAKC,UAAL,CAAgBlC,GAAvB;AAA6B;;AAC1C,YAAbiC,aAAa,CAAChC,KAAD,EAAgB;AAAE,eAAKiC,UAAL,CAAgBlC,GAAhB,GAAsBC,KAAtB;AAA8B;;AAU7C,YAAhBkC,gBAAgB,GAAY;AACnC,iBAAO,KAAKC,cAAL,IAAuB7C,cAAc,CAAC8C,KAA7C;AACH;;AAzDiB,O;;;;;iBAII,E;;;;;;;iBAeI/C,WAAW,CAACgD,U;;;;;;;iBAGd,I;;;;;;;iBAGa;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAEA;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAeC;AAAA;AAAA,kDAAoB,GAApB,C;;;;;;;iBAMf,I;;;;;;;iBAGF,G;;;;;;;iBAGY/C,cAAc,CAACgD,a;;;;;;;iBAUtB,C;;;;;;;iBAGa,E", "sourcesContent": ["\r\nimport { _decorator, error, v2, Vec2, Prefab, Enum, CCInteger } from \"cc\";\r\nimport { ExpressionValue } from \"./bullet/ExpressionValue\";\r\nimport { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType, eWrapMode } from \"./bullet/EventGroupData\";\r\nimport { eEasing } from \"../bullet/Easing\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nexport enum eSpawnOrder {\r\n    Sequential = 0,\r\n    Random = 1,\r\n}\r\n\r\nexport enum eWaveAngleType {\r\n    FacingMoveDir, FacingPlayer, Fixed, \r\n}\r\n\r\nexport enum eWaveConditionType {\r\n    Player_Level,       // 玩家等级\r\n\r\n    Spawn_Index,        // 当前生成的索引\r\n}\r\n\r\nexport enum eWaveActionType {\r\n    Speed,\r\n    SpeedAngle,\r\n}\r\n\r\n// 和发射器的事件组类似\r\n@ccclass(\"WaveConditionData\")\r\nexport class WaveConditionData implements IEventConditionData {\r\n    @property({ type: Enum(eConditionOp), displayName: '条件关系' })\r\n    public op: eConditionOp = eConditionOp.And;\r\n\r\n    @property({visible:false})\r\n    public type: eWaveConditionType = eWaveConditionType.Player_Level;\r\n\r\n    @property({ type: Enum(eCompareOp), displayName: '比较方式' })\r\n    public compareOp: eCompareOp = eCompareOp.Equal;\r\n    \r\n    // 条件值: 例如持续时间、距离\r\n    @property({visible:false})\r\n    public targetValue : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n}\r\n\r\n@ccclass(\"WaveActionData\")\r\nexport class WaveActionData implements IEventActionData {\r\n    @property({visible:false})\r\n    public type: eWaveActionType = eWaveActionType.Speed;\r\n    \r\n    // 持续时间: 0表示立即执行\r\n    @property({visible:false})\r\n    public duration: ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '持续时间' })\r\n    public get durationStr(): string { return this.duration.raw; }\r\n    public set durationStr(value: string) { this.duration.raw = value; }\r\n    \r\n    @property({visible:false})\r\n    public targetValue: ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: '目标值'})\r\n    public get targetValueStr(): string { return this.targetValue.raw; }\r\n    public set targetValueStr(value: string) { this.targetValue.raw = value; }\r\n    \r\n    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })\r\n    public targetValueType: eTargetValueType = eTargetValueType.Absolute;\r\n    \r\n    @property({visible:false})\r\n    public transitionDuration : ExpressionValue = new ExpressionValue('0');\r\n    @property({ displayName: '变换到目标值所需时间' })\r\n    public get transitionDurationStr(): number { return this.transitionDuration.value; }\r\n    public set transitionDurationStr(value: number) { this.transitionDuration.value = value; }\r\n    \r\n    @property({ type: Enum(eWrapMode), displayName: '循环模式' })\r\n    wrapMode: eWrapMode = eWrapMode.Once;\r\n\r\n    @property({ type: Enum(eEasing), displayName: '缓动函数' })\r\n    public easing: eEasing = eEasing.Linear;\r\n}\r\n\r\n@ccclass(\"WaveEventGroupData\")\r\nexport class WaveEventGroupData {\r\n    @property({ displayName: '事件组名称' })\r\n    public name: string = \"\";\r\n\r\n    @property({ type: [WaveConditionData], displayName: '条件列表' })\r\n    public conditions: WaveConditionData[] = [];\r\n\r\n    @property({ type: [WaveActionData], displayName: '行为列表' })\r\n    public actions: WaveActionData[] = [];\r\n}\r\n\r\n/**\r\n * 波次数据：未来代替现有的EnemyWave\r\n * 所有时间相关的，单位都是毫秒(ms)\r\n */\r\n@ccclass(\"WaveData\")\r\nexport class WaveData {\r\n    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发\r\n    // 因此这里不再配置触发条件\r\n    @property({visible:false})\r\n    planeList: number[] = [];\r\n\r\n    @property({displayName: \"飞机列表(,号分隔)\", group: \"基础属性\" })\r\n    public get planeListStr(): string {\r\n        return (this.planeList || []).join(\",\");\r\n    }\r\n    public set planeListStr(value: string) {\r\n        if (value === \"\") {\r\n            this.planeList = [];\r\n            return;\r\n        }\r\n        this.planeList = value.split(\",\").map((item) => parseInt(item));\r\n    }\r\n\r\n    @property({type: Enum(eSpawnOrder), displayName: \"出生顺序\", group: \"基础属性\"})\r\n    spawnOrder: eSpawnOrder = eSpawnOrder.Sequential;\r\n\r\n    @property({displayName: \"出生间隔(ms)\", group: \"基础属性\"})\r\n    spawnInterval: number = 1000;\r\n\r\n    @property({visible:false})\r\n    public spawnPosX : ExpressionValue = new ExpressionValue('0');\r\n    @property({visible:false})\r\n    public spawnPosY : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: \"出生位置X\", group: \"基础属性\"})\r\n    public get spawnPosXStr(): string { return this.spawnPosX.raw; }\r\n    public set spawnPosXStr(value: string) { this.spawnPosX.raw = value; }\r\n    @property({displayName: \"出生位置Y\", group: \"基础属性\"})\r\n    public get spawnPosYStr(): string { return this.spawnPosY.raw; }\r\n    public set spawnPosYStr(value: string) { this.spawnPosY.raw = value; }\r\n\r\n    private _spawnPos: Vec2 = new Vec2();\r\n    public get spawnPos(): Vec2 {\r\n        this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());\r\n        return this._spawnPos;\r\n    }\r\n\r\n    @property({visible:false})\r\n    public spawnAngle : ExpressionValue = new ExpressionValue('0');\r\n    @property({displayName: \"出生角度\", group: \"基础属性\"})\r\n    public get spawnAngleStr(): string { return this.spawnAngle.raw; }\r\n    public set spawnAngleStr(value: string) { this.spawnAngle.raw = value; }\r\n\r\n    @property({type: CCInteger, displayName: \"延迟销毁时间\", tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁', group: \"基础属性\"})\r\n    delayDestroy: number = 5000;\r\n\r\n    @property({type: CCInteger, displayName: \"出生速度\", group: \"基础属性\"})\r\n    spawnSpeed: number = 500;\r\n\r\n    @property({type: Enum(eWaveAngleType), displayName: \"单位朝向类型\", group: \"基础属性\"})\r\n    planeAngleType: eWaveAngleType = eWaveAngleType.FacingMoveDir;\r\n    public get isFixedAngleType(): boolean {\r\n        return this.planeAngleType == eWaveAngleType.Fixed;\r\n    }\r\n\r\n    @property({type: CCInteger, \r\n        // visible() { \r\n        //     return this.isFixedAngleType;\r\n        // },\r\n        displayName: \"单位朝向\", tooltip: '仅在单位朝向类型为Fixed时有效', group: '基础属性'})\r\n    planeAngleFixed: number = 0;\r\n\r\n    @property({type: [WaveEventGroupData], displayName: '事件组', group: '事件组'})\r\n    eventGroupData: WaveEventGroupData[] = [];\r\n}"]}