{"__type__": "sp.SkeletonData", "_name": "skel_bulletlight", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_skeletonJson": {"skeleton": {"hash": "OuRMuJ0PrcDXcleJZwXTTrFrzbk", "spine": "3.6.53", "width": 380, "height": 400, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "color": "fe0f0fff"}], "slots": [{"name": "gg0001", "bone": "bone", "attachment": "gg0001", "blend": "additive"}], "skins": {"default": {"gg0001": {"gg0001": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0002": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0003": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0004": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0005": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0006": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0007": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0008": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0009": {"x": 6, "y": -26, "width": 380, "height": 400}, "gg0010": {"x": 6, "y": -26, "width": 380, "height": 400}}}}, "animations": {"play": {"slots": {"gg0001": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "gg0001"}, {"time": 0.0333, "name": "gg0002"}, {"time": 0.1, "name": "gg0003"}, {"time": 0.1333, "name": "gg0004"}, {"time": 0.1667, "name": "gg0005"}, {"time": 0.2, "name": "gg0006"}, {"time": 0.2333, "name": "gg0007"}, {"time": 0.2667, "name": "gg0008"}, {"time": 0.3, "name": "gg0009"}, {"time": 0.3333, "name": "gg0010"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, "textures": [{"__uuid__": "b0de49dc-4ae9-4c75-baab-017f4d223ab1@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["skel_bulletlight.png"], "scale": 1, "_atlasText": "\nskel_bulletlight.png\nsize: 2048,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ngg0001\n  rotate: false\n  xy: 931, 36\n  size: 148, 135\n  orig: 380, 400\n  offset: 111, 164\n  index: -1\ngg0002\n  rotate: false\n  xy: 756, 1\n  size: 174, 170\n  orig: 380, 400\n  offset: 100, 142\n  index: -1\ngg0003\n  rotate: true\n  xy: 1381, 54\n  size: 242, 282\n  orig: 380, 400\n  offset: 69, 46\n  index: -1\ngg0004\n  rotate: true\n  xy: 1664, 52\n  size: 239, 266\n  orig: 380, 400\n  offset: 59, 75\n  index: -1\ngg0005\n  rotate: false\n  xy: 1093, 195\n  size: 287, 316\n  orig: 380, 400\n  offset: 37, 48\n  index: -1\ngg0006\n  rotate: false\n  xy: 756, 172\n  size: 336, 339\n  orig: 380, 400\n  offset: 14, 45\n  index: -1\ngg0007\n  rotate: false\n  xy: 382, 141\n  size: 373, 370\n  orig: 380, 400\n  offset: 0, 30\n  index: -1\ngg0008\n  rotate: false\n  xy: 1, 132\n  size: 380, 379\n  orig: 380, 400\n  offset: 0, 21\n  index: -1\ngg0009\n  rotate: false\n  xy: 1381, 297\n  size: 316, 214\n  orig: 380, 400\n  offset: 0, 80\n  index: -1\ngg0010\n  rotate: false\n  xy: 1698, 292\n  size: 307, 219\n  orig: 380, 400\n  offset: 0, 74\n  index: -1\n"}