{"__type__": "sp.SkeletonData", "_name": "skel_winefire", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_skeletonJson": {"skeleton": {"hash": "+3BfMSRbd+BMl72Y+6jBRlrCo2M", "spine": "3.6.53", "width": 144, "height": 143, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "1", "parent": "root", "color": "ff0000ff"}, {"name": "3", "parent": "root", "color": "ffed00ff"}, {"name": "bone", "parent": "root", "length": 24.86, "rotation": 65.35, "x": 14.04, "y": 24.84, "color": "0f00ffff"}, {"name": "bone2", "parent": "root", "length": 24.86, "rotation": 150.13, "x": -22.76, "y": 13.97, "scaleX": 1.215, "color": "0f00ffff"}, {"name": "bone3", "parent": "root", "length": 24.86, "rotation": -115.69, "x": -10.66, "y": -20.12, "scaleX": 0.974, "scaleY": 0.873, "color": "0f00ffff"}, {"name": "bone4", "parent": "root", "length": 24.86, "rotation": -15.89, "x": 24.67, "y": -5.3, "scaleX": 0.974, "scaleY": 0.873, "color": "0f00ffff"}], "slots": [{"name": "xx11", "bone": "bone4", "attachment": "xx0009"}, {"name": "1", "bone": "1", "attachment": "xx0001"}, {"name": "3", "bone": "3", "attachment": "xx0005"}, {"name": "xx0009", "bone": "bone", "attachment": "xx0009"}, {"name": "xx9", "bone": "bone2", "attachment": "xx0009"}, {"name": "xx10", "bone": "bone3", "attachment": "xx0009"}], "skins": {"default": {"1": {"xx0001": {"width": 144, "height": 143}, "xx0002": {"width": 144, "height": 143}, "xx0003": {"width": 143, "height": 144}, "xx0004": {"width": 143, "height": 144}}, "3": {"xx0005": {"x": 0.9, "width": 111, "height": 113}, "xx0006": {"x": 0.9, "width": 111, "height": 114}, "xx0007": {"x": 0.9, "width": 111, "height": 113}, "xx0008": {"x": 0.9, "width": 111, "height": 113}}, "xx0009": {"xx0009": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}, "xx0010": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0011": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0012": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}}, "xx10": {"xx0009": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}, "xx0010": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0011": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0012": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}}, "xx11": {"xx0009": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}, "xx0010": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0011": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0012": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}}, "xx9": {"xx0009": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}, "xx0010": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0011": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 26, "height": 46}, "xx0012": {"x": 15.18, "y": -0.39, "rotation": -89.48, "width": 25, "height": 46}}}}, "animations": {"fire1": {"slots": {"1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "xx0001"}]}, "3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0005"}]}, "xx0009": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0009"}]}, "xx9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0009"}]}, "xx10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0009"}]}, "xx11": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0009"}]}}, "bones": {"1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 0.924, "y": 0.924}, {"time": 0.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.621, "y": 1.621}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 36.34, "y": -9.08}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}, {"time": 0.3333, "x": 11.06, "y": 25.28}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -34.76, "y": 18.17}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -16.19, "y": -30.81}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}, "fire2": {"slots": {"1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "xx0002"}]}, "3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0006"}]}, "xx0009": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0010"}]}, "xx9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0010"}]}, "xx10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0010"}]}, "xx11": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0010"}]}}, "bones": {"1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 0.924, "y": 0.924}, {"time": 0.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.621, "y": 1.621}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 36.34, "y": -9.08}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}, {"time": 0.3333, "x": 11.06, "y": 25.28}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -34.76, "y": 18.17}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -16.19, "y": -30.81}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}, "fire3": {"slots": {"1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "xx0003"}]}, "3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0007"}]}, "xx0009": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0011"}]}, "xx9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0011"}]}, "xx10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0011"}]}, "xx11": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0011"}]}}, "bones": {"1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 0.924, "y": 0.924}, {"time": 0.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.621, "y": 1.621}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 36.34, "y": -9.08}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}, {"time": 0.3333, "x": 11.06, "y": 25.28}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -34.76, "y": 18.17}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -16.19, "y": -30.81}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}, "fire4": {"slots": {"1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "xx0004"}]}, "3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0008"}]}, "xx0009": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0012"}]}, "xx9": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0012"}]}, "xx10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0012"}]}, "xx11": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "xx0012"}]}}, "bones": {"1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 0.924, "y": 0.924}, {"time": 0.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0667, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0667, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.621, "y": 1.621}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 36.34, "y": -9.08}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}, {"time": 0.3333, "x": 11.06, "y": 25.28}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -34.76, "y": 18.17}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": -16.19, "y": -30.81}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, "textures": [{"__uuid__": "46a1d316-c5aa-4f37-8c4d-b3663baf6225@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["skel_winefire.png"], "scale": 1, "_atlasText": "\nskel_winefire.png\nsize: 1024,256\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nxx0001\n  rotate: false\n  xy: 1, 112\n  size: 144, 143\n  orig: 144, 143\n  offset: 0, 0\n  index: -1\nxx0002\n  rotate: false\n  xy: 146, 112\n  size: 144, 143\n  orig: 144, 143\n  offset: 0, 0\n  index: -1\nxx0003\n  rotate: true\n  xy: 291, 112\n  size: 143, 144\n  orig: 143, 144\n  offset: 0, 0\n  index: -1\nxx0004\n  rotate: true\n  xy: 436, 112\n  size: 143, 144\n  orig: 143, 144\n  offset: 0, 0\n  index: -1\nxx0005\n  rotate: false\n  xy: 693, 142\n  size: 111, 113\n  orig: 111, 113\n  offset: 0, 0\n  index: -1\nxx0006\n  rotate: false\n  xy: 581, 141\n  size: 111, 114\n  orig: 111, 114\n  offset: 0, 0\n  index: -1\nxx0007\n  rotate: false\n  xy: 805, 142\n  size: 111, 113\n  orig: 111, 113\n  offset: 0, 0\n  index: -1\nxx0008\n  rotate: false\n  xy: 581, 27\n  size: 111, 113\n  orig: 111, 113\n  offset: 0, 0\n  index: -1\nxx0009\n  rotate: true\n  xy: 1, 1\n  size: 25, 46\n  orig: 25, 46\n  offset: 0, 0\n  index: -1\nxx0010\n  rotate: false\n  xy: 693, 95\n  size: 26, 46\n  orig: 26, 46\n  offset: 0, 0\n  index: -1\nxx0011\n  rotate: false\n  xy: 917, 209\n  size: 26, 46\n  orig: 26, 46\n  offset: 0, 0\n  index: -1\nxx0012\n  rotate: false\n  xy: 693, 48\n  size: 25, 46\n  orig: 25, 46\n  offset: 0, 0\n  index: -1\n"}