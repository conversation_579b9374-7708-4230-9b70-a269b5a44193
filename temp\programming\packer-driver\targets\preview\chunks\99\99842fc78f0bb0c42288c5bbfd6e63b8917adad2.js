System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, RPNCalculator, SerializableRPNProgram, _decorator, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _class3, _crd, ccclass, property, ExpressionValue;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfRPNCalculator(extras) {
    _reporterNs.report("RPNCalculator", "db://assets/bundles/common/script/game/utils/RPN", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRPNProgram(extras) {
    _reporterNs.report("RPNProgram", "db://assets/bundles/common/script/game/utils/RPN", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRPNContext(extras) {
    _reporterNs.report("RPNContext", "db://assets/bundles/common/script/game/utils/RPN", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSerializableRPNProgram(extras) {
    _reporterNs.report("SerializableRPNProgram", "db://assets/bundles/common/script/game/utils/RPN", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      RPNCalculator = _unresolved_2.default;
      SerializableRPNProgram = _unresolved_2.SerializableRPNProgram;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "258e0pTWvVGB5/gJRA6yy8P", "ExpressionValue", undefined);

      __checkObsolete__(['_decorator', 'Enum']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ExpressionValue", ExpressionValue = (_dec = ccclass('ExpressionValue'), _dec2 = property({
        visible: false
      }), _dec3 = property({
        visible: false
      }), _dec4 = property({
        visible: false
      }), _dec5 = property({
        type: _crd && SerializableRPNProgram === void 0 ? (_reportPossibleCrUseOfSerializableRPNProgram({
          error: Error()
        }), SerializableRPNProgram) : SerializableRPNProgram,
        visible: false
      }), _dec6 = property({
        displayName: '值'
      }), _dec(_class = (_class2 = (_class3 = class ExpressionValue {
        get raw() {
          return this.isExpression ? this.expression : this.value.toString();
        }

        set raw(value) {
          // 替换全角为半角
          this.expression = value.replace(/[\uff01-\uff5e]/g, ch => String.fromCharCode(ch.charCodeAt(0) - 0xfee0)); // if includes any 'letters' then it is a expression

          this.isExpression = /[a-zA-Z]/.test(value);

          if (!this.isExpression) {
            // try parse number
            var parsed = parseFloat(value);

            if (!isNaN(parsed)) {
              this.value = parsed;
            }
          } else {
            // Compile and store both runtime and serializable versions
            this.program = ExpressionValue.calc.compile(this.expression);
            this.serializedProgram = (_crd && SerializableRPNProgram === void 0 ? (_reportPossibleCrUseOfSerializableRPNProgram({
              error: Error()
            }), SerializableRPNProgram) : SerializableRPNProgram).fromRPNProgram(this.program);
          }
        }

        eval(context) {
          if (context === void 0) {
            context = null;
          }

          if (this.isExpression) {
            var _this$serializedProgr;

            if (!this.program && (_this$serializedProgr = this.serializedProgram) != null && _this$serializedProgr.isCompiled()) {
              this.program = this.serializedProgram.toRPNProgram(ExpressionValue.calc.registry);
            }

            if (this.program) {
              var result = this.program.evaluate(context || {});
              return typeof result === 'number' ? result : result ? 1 : 0; // return 0 if false else 1
            }

            return this.value;
          }

          return this.value;
        } // Get debug info about the compiled program


        getDebugInfo() {
          var _this$serializedProgr2, _this$serializedProgr3, _this$serializedProgr4, _this$serializedProgr5;

          if (!this.isExpression) {
            return {
              type: 'number',
              value: this.value
            };
          }

          return {
            type: 'expression',
            expression: this.expression,
            isCompiled: (_this$serializedProgr2 = this.serializedProgram) == null ? void 0 : _this$serializedProgr2.isCompiled(),
            variables: (_this$serializedProgr3 = this.serializedProgram) == null ? void 0 : _this$serializedProgr3.varNames,
            codeLength: (_this$serializedProgr4 = this.serializedProgram) == null ? void 0 : _this$serializedProgr4.code.length,
            constantsCount: (_this$serializedProgr5 = this.serializedProgram) == null ? void 0 : _this$serializedProgr5.consts.length
          };
        }

        constructor(str) {
          _initializerDefineProperty(this, "value", _descriptor, this);

          _initializerDefineProperty(this, "isExpression", _descriptor2, this);

          _initializerDefineProperty(this, "expression", _descriptor3, this);

          // Serializable compiled program for editor storage
          _initializerDefineProperty(this, "serializedProgram", _descriptor4, this);

          // Runtime program (not serialized)
          this.program = null;
          this.raw = str || '';
        }

      }, _class3.calc = new (_crd && RPNCalculator === void 0 ? (_reportPossibleCrUseOfRPNCalculator({
        error: Error()
      }), RPNCalculator) : RPNCalculator)(), _class3), (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "value", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "isExpression", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return false;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "expression", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return '';
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "serializedProgram", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "raw", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "raw"), _class2.prototype)), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=99842fc78f0bb0c42288c5bbfd6e63b8917adad2.js.map