import { color, Color, v2, Vec2 } from "cc";
import { Tools } from "../utils/Tools";
import { TrackGroup } from "./EnemyWave";

/**
 * Boss 基础数据类
 */
export class BossBaseData {
    id: number = 0;
    atlas: string[] = [];
    exp: number = 0;
    collideArr: number[] = [];
    attack: number = 0;
    collideAttack: number = 0;
    transformAudio: string = "";
    blastParam: number[] = [];
    blastShake: Vec2[] = [];
    appearParam: number[] = [];
    onlyLoot: number[] = [];
    lootArr: number[] = [];
    lootParam0: number[] = [];
    lootParam1: number[] = [];

    /**
     * 从 JSON 数据加载 Boss 基础数据
     * @param data JSON 数据
     */
    loadJson(data: any): void {
        if (data.hasOwnProperty("id")) this.id = parseInt(data.id);
        if (data.hasOwnProperty("atlas")) this.atlas = data.atlas.split(";");
        if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
        if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;

        if (data.hasOwnProperty("cs") && data.cs !== "") {
            const csArray = data.cs.split(";");
            for (const cs of csArray) {
                if (cs !== "") this.collideArr = Tools.stringToNumber(cs, ",");
            }
        }

        if (data.hasOwnProperty("bla") && data.bla !== "") {
            const blaArray = data.bla.split(";");
            for (const bla of blaArray) {
                if (bla !== "") this.blastParam = Tools.stringToNumber(bla, ",");
            }
        }

        if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");
            for (const sk of skArray) {
                if (sk !== "") this.blastShake.push(Tools.stringToPoint(sk, ","));
            }
        }

        if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
        if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);
        if (data.hasOwnProperty("app")) this.appearParam = Tools.stringToNumber(data.app, ",");
        if (data.hasOwnProperty("fl") && data.fl !== "") this.onlyLoot = Tools.stringToNumber(data.fl, ",");
        if (data.hasOwnProperty("loot")) this.lootArr = Tools.stringToNumber(data.loot, ",");
        if (data.hasOwnProperty("lp0")) this.lootParam0 = Tools.stringToNumber(data.lp0, ",");
        if (data.hasOwnProperty("lp1")) this.lootParam1 = Tools.stringToNumber(data.lp1, ",");
    }
}

/**
 * Boss 数据类
 */
export class BossData extends BossBaseData {
    subId: number = 0;
    units: number[] = [];
    unitsOrder: number[] = [];
    hpParam: number[] = [];
    appearParam: number[] = [];
    transformAudio: string = "";
    blastType: number = 0;
    bombHurt: number = 0;
    leave: number = 0;
    nextBoss: number[] = [];
    wayPointXs: number[] = [];
    wayPointYs: number[] = [];
    wayPointIntervals: number[] = [];
    speeds: number[] = [];
    attackIntervals: number[] = [];
    snakeParam: any[] = [];
    trackGroups: TrackGroup[] = [];
    attackActions: any[] = [];
    attackPoints: any[] = [];
    dieFallDelay: number = 0;
    blastCount: number = 0;
    va: number[] = [];
    dashTrack: number[] = [];
    freeTrackArr: number[] = [];
    enemyId: number = 0;
    enemyRotate: number = 0;
    enemyPos: Vec2[] = [];
    enemyTrackGroup1: TrackGroup[] = [];
    enemyTrackGroup2: TrackGroup[] = [];

    /**
     * 从 JSON 数据加载 Boss 数据
     * @param data JSON 数据
     */
    loadJson(data: any): void {
        if (data.hasOwnProperty("bId")) this.id = parseInt(data.bId);
        if (data.hasOwnProperty("sId")) this.subId = parseInt(data.sId);
        if (data.hasOwnProperty("us")) this.units = Tools.stringToNumber(data.us, ",");
        if (data.hasOwnProperty("rid") && data.rid !== "") this.nextBoss = Tools.stringToNumber(data.rid, ",");
        if (data.hasOwnProperty("exp")) this.exp = parseInt(data.exp);
        if (data.hasOwnProperty("leave")) this.leave = parseInt(data.leave);
        if (data.hasOwnProperty("va")) this.va = Tools.stringToNumber(data.va, ",");

        if (data.hasOwnProperty("ua")) {
            const uaArray = data.ua.split(";");
            for (const ua of uaArray) {
                if (ua !== "") this.unitsOrder = Tools.stringToNumber(ua, ",");
            }
        }

        if (data.hasOwnProperty("hpp")) this.hpParam = Tools.stringToNumber(data.hpp, ",");
        if (data.hasOwnProperty("app")) this.appearParam = Tools.stringToNumber(data.app, ",");
        if (data.hasOwnProperty("ta")) this.transformAudio = data.ta;
        if (data.hasOwnProperty("bla")) this.blastType = Number(data.bla);
        if (data.hasOwnProperty("bh")) this.bombHurt = Number(data.bh);
        if (data.hasOwnProperty("atk")) this.attack = parseInt(data.atk);
        if (data.hasOwnProperty("col")) this.collideAttack = parseInt(data.col);

        if (data.hasOwnProperty("dh")) {
            const dhArray = data.dh.split(";");
            for (const dh of dhArray) {
                if (dh !== "") this.dashTrack = Tools.stringToNumber(dh, ",");
            }
        }

        if (data.hasOwnProperty("ea") && data.ea !== "") {
            this.freeTrackArr = Tools.stringToNumber(data.ea, ",");
        }

        if (data.hasOwnProperty("way") && data.way !== "") {
            const wayArray = data.way.split(";");
            for (const way of wayArray) {
                if (way !== "") {
                    const point = Tools.stringToPoint(way, ",");
                    this.wayPointXs.push(point.x);
                    this.wayPointYs.push(point.y);
                }
            }
        }

        if (data.hasOwnProperty("wi") && data.wi !== "") {
            this.wayPointIntervals = Tools.stringToNumber(data.wi, ",");
        }

        if (data.hasOwnProperty("sp") && data.sp !== "") {
            this.speeds = Tools.stringToNumber(data.sp, ",");
        }

        if (data.hasOwnProperty("ai") && data.ai !== "") {
            this.attackIntervals = Tools.stringToNumber(data.ai, ",");
        }

        if (data.hasOwnProperty("ra") && data.ra !== "") {
            const raArray = data.ra.split(";");
            for (let i = 0; i < raArray.length; i++) {
                if (raArray[i] !== "") {
                    const attackAction = new BossAttackActionData();
                    attackAction.loadJson(raArray[i]);
                    this.attackActions.push(attackAction);
                }
            }
        }
        
        // 解析攻击点数据
        let attackPointIndex = 0;
        while (true) {
            const attackPointKey = "a" + attackPointIndex++;
            if (!data.hasOwnProperty(attackPointKey) || data[attackPointKey] === "") break;
        
            const attackPointData = new BossAttackPointData();
            attackPointData.loadJson(data[attackPointKey]);
            this.attackPoints.push(attackPointData);
        }
        
        // 解析爆炸参数
        if (data.hasOwnProperty("blp") && data.blp !== "") {
            const blpArray = data.blp.split(";");
            for (let i = 0; i < blpArray.length; i++) {
                if (blpArray[i] !== "") {
                    this.blastParam = Tools.stringToNumber(blpArray[i], ",");
                    this.blastCount++;
                }
            }
        }
        
        // 解析爆炸震动参数
        if (data.hasOwnProperty("sk")) {
            const skArray = data.sk.split(";");
            for (let i = 0; i < skArray.length; i++) {
                if (skArray[i] !== "") {
                    this.blastShake.push(Tools.stringToPoint(skArray[i], ","));
                }
            }
        }
        
        // 解析死亡掉落延迟
        if (data.hasOwnProperty("ft")) {
            this.dieFallDelay = Number(data.ft);
        }
        
        // 解析唯一掉落
        if (data.hasOwnProperty("fl") && data.fl !== "") {
            this.onlyLoot = Tools.stringToNumber(data.fl, ",");
        }
        
        // 解析掉落数组
        if (data.hasOwnProperty("loot")) {
            this.lootArr = Tools.stringToNumber(data.loot, ",");
        }
        
        // 解析掉落参数 0
        if (data.hasOwnProperty("lp0")) {
            this.lootParam0 = Tools.stringToNumber(data.lp0, ",");
        }
        
        // 解析掉落参数 1
        if (data.hasOwnProperty("lp1")) {
            this.lootParam1 = Tools.stringToNumber(data.lp1, ",");
        }
        
        // 解析敌人 ID
        if (data.hasOwnProperty("eid")) {
            this.enemyId = Number(data.eid);
        }
        
        // 解析敌人旋转角度
        if (data.hasOwnProperty("erotate")) {
            this.enemyRotate = Number(data.erotate);
        }
        
        // 解析敌人位置
        if (data.hasOwnProperty("epos")) {
            const eposArray = data.epos.split("#");
            for (let i = 0; i < eposArray.length; i++) {
                const position = Tools.stringToNumber(eposArray[i], ",");
                if (position.length === 2) {
                    this.enemyPos.push(v2(position[0], position[1]));
                }
            }
        }
        
        // 解析敌人轨迹组 1
        if (data.hasOwnProperty("etrack1")) {
            const etrack1Array = data.etrack1.split("#");
            for (let i = 0; i < etrack1Array.length; i++) {
                if (etrack1Array[i] !== "" && etrack1Array[i].split(";").length > 1) {
                    const trackGroup = new TrackGroup();
                    trackGroup.loadJson(etrack1Array[i]);
                    this.enemyTrackGroup1.push(trackGroup);
                }
            }
        }
        
        // 解析敌人轨迹组 2
        if (data.hasOwnProperty("etrack2")) {
            const etrack2Array = data.etrack2.split("#");
            for (let i = 0; i < etrack2Array.length; i++) {
                if (etrack2Array[i] !== "" && etrack2Array[i].split(";").length > 1) {
                    const trackGroup = new TrackGroup();
                    trackGroup.loadJson(etrack2Array[i]);
                    this.enemyTrackGroup2.push(trackGroup);
                }
            }
        }
    }
    
}

/**
 * Boss 攻击点数据类
 */
export class BossAttackPointData {
    bAvailable: boolean = true;
    atkType: number = 0;
    atkUnitId: number = 0;
    atkAnim: number[] = [];
    x: number = 0;
    y: number = 0;
    shootInterval: number[] = [];
    bulletIDs: number[] = [];
    bulletNums: number[] = [];
    bulletIntervals: number[] = [];
    bulletAttackRates: number[] = [];
    attackOverDelay: number[] = [];
    waveIds: number[] = [];

    /**
     * 从 JSON 数据加载攻击点数据
     * @param data JSON 数据
     */
    loadJson(data: string): void {
        const parts = data.split("#");
        if (parts.length < 3) {
            Tools.error("BossAttackPointData error:", data);
            return;
        }

        this.atkType = parseInt(parts[0]);
        this.atkUnitId = parseInt(parts[1]);

        const animParts = parts[2].split(";");
        for (const anim of animParts) {
            if (anim !== "") {
                this.atkAnim = Tools.stringToNumber(anim, ",");
            }
        }

        switch (this.atkType) {
            case 0: // 普通攻击
                const attackParts = parts[3].split(";");
                try {
                    if (attackParts.length <= 1) {
                        this.bAvailable = false;
                        return;
                    }

                    const position = Tools.stringToPoint(attackParts[0], ",");
                    this.x = position.x;
                    this.y = position.y;

                    for (let i = 1; i < attackParts.length; i++) {
                        if (attackParts[i] !== "") {
                            const attackData = Tools.stringToNumber(attackParts[i], ",");
                            this.shootInterval.push(attackData[0]);
                            this.bulletIDs.push(attackData[1]);
                            this.bulletNums.push(attackData[2]);
                            this.bulletIntervals.push(attackData[3]);
                            this.bulletAttackRates.push(attackData[4] / 100);
                            this.attackOverDelay.push(attackData[5]);
                        }
                    }
                } catch (error) {
                    Tools.error("BossAttackPointData error:", data);
                }
                break;

            case 1: // 波次攻击
                const waveParts = parts[3].split(";");
                try {
                    if (waveParts.length <= 1) {
                        this.bAvailable = false;
                        return;
                    }

                    const wavePosition = Tools.stringToPoint(waveParts[0], ",");
                    this.x = wavePosition.x;
                    this.y = wavePosition.y;

                    this.waveIds = Tools.stringToNumber(waveParts[1], ",");
                    if (waveParts.length > 2) {
                        this.attackOverDelay.push(parseInt(waveParts[2]));
                    }
                } catch (error) {
                    Tools.error("BossAttackPointData error:", data);
                }
                break;

            default:
                Tools.error("Unknown attack type:", this.atkType);
                break;
        }
    }
}
/**
 * Boss 攻击动作数据类
 */
export class BossAttackActionData {
    bAtkMove: boolean = false; // 是否移动攻击
    atkActId: number = 0; // 攻击动作 ID
    atkPointId: number[] = []; // 攻击点 ID 列表

    /**
     * 从 JSON 数据加载攻击动作数据
     * @param data JSON 数据
     */
    loadJson(data: string): void {
        const parts = Tools.stringToNumber(data, ",");
        try {
            if (parts.length > 1) {
                this.bAtkMove = parts[0] === 1;
                this.atkActId = parts[1];
                for (let i = 2; i < parts.length; i++) {
                    this.atkPointId.push(parts[i]);
                }
            }
        } catch (error) {
            Tools.error("BossAttackActionData error:", data);
        }
    }
}
