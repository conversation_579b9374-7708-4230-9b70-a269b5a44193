{"__type__": "sp.SkeletonData", "_name": "skel_relief", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_skeletonJson": {"skeleton": {"hash": "pXicXP7h6glKSdlkfTJHb1jm/S8", "spine": "3.6.53", "width": 544.01, "height": 540.34, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone2", "parent": "root", "x": 205.5, "y": 46.5, "color": "feff00ff"}, {"name": "d", "parent": "root", "color": "ff0000ff"}, {"name": "g", "parent": "root", "color": "0020ffff"}, {"name": "quan", "parent": "root", "color": "feff00ff"}, {"name": "quan2", "parent": "root", "color": "feff00ff"}, {"name": "x0001", "parent": "bone2", "length": 114.02, "rotation": 157.6, "x": -242, "y": -35, "color": "feff00ff"}, {"name": "x1", "parent": "bone2", "length": 114.02, "rotation": 76.75, "x": -188, "y": -10, "scaleX": 0.833, "scaleY": 0.833, "color": "feff00ff"}, {"name": "x2", "parent": "bone2", "length": 114.02, "rotation": -36.76, "x": -173, "y": -71, "scaleX": 0.833, "scaleY": 0.833, "color": "feff00ff"}, {"name": "x3", "parent": "bone2", "length": 114.02, "rotation": -117.65, "x": -251.34, "y": -108.34, "scaleX": 0.911, "scaleY": 0.911, "color": "feff00ff"}, {"name": "x4", "parent": "bone2", "length": 114.02, "rotation": 47.12, "x": -176.4, "y": -30.88, "scaleX": 0.833, "scaleY": 0.833, "color": "feff00ff"}, {"name": "x5", "parent": "bone2", "length": 114.02, "rotation": -150.3, "x": -243.45, "y": -78.12, "scaleX": 0.587, "scaleY": 0.587, "color": "feff00ff"}], "slots": [{"name": "x0001", "bone": "x0001", "attachment": "x0001", "blend": "additive"}, {"name": "q2", "bone": "quan2", "attachment": "q"}, {"name": "x1", "bone": "x1", "attachment": "x0001", "blend": "additive"}, {"name": "x4", "bone": "x4", "attachment": "x0001", "blend": "additive"}, {"name": "x2", "bone": "x2", "attachment": "x0001", "blend": "additive"}, {"name": "x3", "bone": "x3", "attachment": "x0001", "blend": "additive"}, {"name": "x5", "bone": "x5", "attachment": "x0001", "blend": "additive"}, {"name": "d", "bone": "d", "attachment": "d", "blend": "additive"}, {"name": "q", "bone": "quan", "attachment": "q", "blend": "additive"}, {"name": "g", "bone": "g", "attachment": "g", "blend": "additive"}], "skins": {"default": {"d": {"d": {"x": -1.43, "y": -5.87, "width": 256, "height": 256}}, "g": {"g": {"width": 128, "height": 128}}, "q": {"q": {"width": 460, "height": 231}}, "q2": {"q": {"width": 460, "height": 231}}, "x0001": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}, "x1": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}, "x2": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}, "x3": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}, "x4": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}, "x5": {"x0001": {"x": 71.5, "y": -0.75, "rotation": -91.01, "width": 297, "height": 288}}}}, "animations": {"play": {"slots": {"d": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "d"}]}, "g": {"color": [{"time": 0, "color": "22fff0ff"}, {"time": 0.1333, "color": "21fff0ff"}, {"time": 0.2667, "color": "21fff000"}], "attachment": [{"time": 0, "name": "g"}]}, "q": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "q"}]}, "q2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "q"}]}, "x0001": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}]}, "x1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}, {"time": 0.2, "name": "x0001"}]}, "x2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}]}, "x3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}]}, "x4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}]}, "x5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "x0001"}]}}, "bones": {"x0001": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.5333, "x": -61.86, "y": 33.74}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 1}, {"time": 0.5333, "x": 1, "y": 0.388}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}]}, "x1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.4333, "x": 9.84, "y": 37.96}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.4333, "x": 1, "y": 0.576}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}]}, "x2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.4333, "x": 32.34, "y": -28.12}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.4333, "x": 1.146, "y": 0.553}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}]}, "x3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.5, "x": -30.93, "y": -57.65}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.5, "x": 1.063, "y": 0.378}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}]}, "g": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1.6, "y": 1.6}, {"time": 0.1333, "x": 2.322, "y": 2.322}, {"time": 0.2667, "x": 0.7, "y": 0.7}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "d": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}, {"time": 0.5, "angle": -70}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3667, "x": 2.06, "y": 12.38}, {"time": 0.5, "x": 4.13, "y": 12.38}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 0.783, "y": 0.783}, {"time": 0.3667, "x": 2.703, "y": 2.703}, {"time": 0.5, "x": 3, "y": 3}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}]}, "quan": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1333, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.6, "y": 0.6}, {"time": 0.1333, "x": 0.5, "y": 0.5}, {"time": 0.2333, "x": 1.3, "y": 1.3}, {"time": 0.4, "x": 1.9, "y": 1.9}, {"time": 0.6333, "x": 2.1, "y": 2.1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0}]}, "x4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.5, "x": 32.34, "y": 29.53}], "scale": [{"time": 0, "x": 1.2, "y": 1.2, "curve": "stepped"}, {"time": 0.2333, "x": 1.2, "y": 1.2}, {"time": 0.5, "x": 1.392, "y": 0.617}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}]}, "quan2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.5, "y": 0.5, "curve": "stepped"}, {"time": 0.2333, "x": 0.5, "y": 0.5}, {"time": 0.4667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0}]}, "x5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.5, "x": -145.25, "y": -79.98}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1}, {"time": 0.5, "x": 1.063, "y": 0.378}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}]}}}}}, "textures": [{"__uuid__": "15a2d9c1-ccb7-40a6-8626-ccc31d9be3c0@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["skel_relief.png"], "scale": 1, "_atlasText": "\nskel_relief.png\nsize: 1024,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nd\n  rotate: false\n  xy: 1, 1\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ng\n  rotate: false\n  xy: 760, 361\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nq\n  rotate: false\n  xy: 1, 258\n  size: 460, 231\n  orig: 460, 231\n  offset: 0, 0\n  index: -1\nx0001\n  rotate: false\n  xy: 462, 201\n  size: 297, 288\n  orig: 297, 288\n  offset: 0, 0\n  index: -1\n"}