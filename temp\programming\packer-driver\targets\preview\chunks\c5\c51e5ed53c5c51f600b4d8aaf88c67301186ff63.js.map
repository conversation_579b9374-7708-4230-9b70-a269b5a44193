{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/game/data/EnemyWave.ts"], "names": ["EnemyWave", "TrackGroup", "WaveLootData", "Tools", "enemyGroupID", "groupInterval", "type", "enemyID", "enemyInterval", "posDX", "posDY", "enemyNum", "bSetStartPos", "startPosX", "startPosY", "trackGroups", "liveParam", "exp", "rotateSpeed", "firstShootDelay", "loadJson", "data", "delay", "planeType", "planeId", "interval", "num", "rotatioSpeed", "point", "stringToPoint", "offsetPos", "x", "y", "hasOwnProperty", "startPos", "stringToNumber", "pos", "length", "ways", "track", "split", "way", "trackGroup", "push", "types", "trackParams", "i", "FirstShootDelay", "fromLevelWave", "wave", "posX", "posY", "enemyWave", "loopNum", "formIndex", "trackIDs", "speeds", "accelerates", "trackIntervals", "parts", "header", "part", "values", "enemys", "lootId", "parseInt"], "mappings": ";;;sFAQaA,S,EAwGAC,U,EAmCAC,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjJJC,MAAAA,K,iBAAAA,K;;;;;;;;;AAGT;AACA;AACA;2BACaH,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACnBI,YADmB,GACI,CADJ;AAAA,eAEnBC,aAFmB,GAEK,CAFL;AAAA,eAGnBC,IAHmB,GAGJ,CAHI;AAAA,eAInBC,OAJmB,GAID,CAJC;AAAA,eAKnBC,aALmB,GAKK,CALL;AAAA,eAMnBC,KANmB,GAMH,CANG;AAAA,eAOnBC,KAPmB,GAOH,CAPG;AAAA,eAQnBC,QARmB,GAQA,CARA;AAAA,eASnBC,YATmB,GASK,KATL;AAAA,eAUnBC,SAVmB,GAUC,CAVD;AAAA,eAWnBC,SAXmB,GAWC,CAXD;AAAA,eAYnBC,WAZmB,GAYS,EAZT;AAAA,eAanBC,SAbmB,GAaG,EAbH;AAAA,eAcnBC,GAdmB,GAcL,CAdK;AAenB;AACA;AAhBmB,eAiBnBC,WAjBmB,GAiBG,CAjBH;AAAA,eAkBnBC,eAlBmB,GAkBS,EAlBT;AAAA;;AAoBnB;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,IAAD,EAAsB;AAC1B,eAAKjB,YAAL,GAAoBiB,IAAI,CAACjB,YAAzB;AACA,eAAKC,aAAL,GAAqBgB,IAAI,CAACC,KAA1B;AACA,eAAKhB,IAAL,GAAYe,IAAI,CAACE,SAAjB;AACA,eAAKhB,OAAL,GAAec,IAAI,CAACG,OAApB;AACA,eAAKhB,aAAL,GAAqBa,IAAI,CAACI,QAA1B;AACA,eAAKd,QAAL,GAAgBU,IAAI,CAACK,GAArB;AACA,eAAKR,WAAL,GAAmBG,IAAI,CAACM,YAAxB;AAGA,cAAMC,KAAK,GAAG;AAAA;AAAA,8BAAMC,aAAN,CAAoBR,IAAI,CAACS,SAAzB,EAAoC,GAApC,CAAd;AACA,eAAKrB,KAAL,GAAamB,KAAK,CAACG,CAAnB;AACA,eAAKrB,KAAL,GAAakB,KAAK,CAACI,CAAnB;;AAEA,cAAIX,IAAI,CAACY,cAAL,CAAoB,KAApB,CAAJ,EAAgC;AAC5B,gBAAMC,QAAQ,GAAG;AAAA;AAAA,gCAAMC,cAAN,CAAqBd,IAAI,CAACe,GAA1B,EAA+B,GAA/B,CAAjB;;AACA,gBAAIF,QAAQ,CAACG,MAAT,KAAoB,CAAxB,EAA2B;AACvB,mBAAKxB,SAAL,GAAiBqB,QAAQ,CAAC,CAAD,CAAzB;AACA,mBAAKpB,SAAL,GAAiBoB,QAAQ,CAAC,CAAD,CAAzB;AACA,mBAAKtB,YAAL,GAAoB,IAApB;AACH,aAJD,MAIO;AACH,mBAAKA,YAAL,GAAoB,KAApB;AACH;AACJ;;AAGD,cAAIS,IAAI,CAACY,cAAL,CAAoB,OAApB,CAAJ,EAAkC;AAC9B,gBAAMK,IAAI,GAAGjB,IAAI,CAACkB,KAAL,CAAWC,KAAX,CAAiB,GAAjB,CAAb;;AACA,iBAAK,IAAMC,GAAX,IAAkBH,IAAlB,EAAwB;AACpB,kBAAIG,GAAG,KAAK,EAAR,IAAcA,GAAG,CAACD,KAAJ,CAAU,GAAV,EAAeH,MAAf,GAAwB,CAA1C,EAA6C;AACzC,oBAAMK,UAAU,GAAG,IAAIzC,UAAJ,EAAnB;AACAyC,gBAAAA,UAAU,CAACtB,QAAX,CAAoBqB,GAApB;AACA,qBAAK1B,WAAL,CAAiB4B,IAAjB,CAAsBD,UAAtB;AACH;AACJ;AACJ;;AACD,cAAIrB,IAAI,CAACY,cAAL,CAAoB,aAApB,CAAJ,EAAwC;AACpC,gBAAMW,KAAK,GAAG;AAAA;AAAA,gCAAMT,cAAN,CAAqBd,IAAI,CAACwB,WAA1B,EAAuC,GAAvC,CAAd;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAAK,CAACP,MAA1B,EAAkCS,CAAC,EAAnC,EAAuC;AACnC,kBAAI,KAAK/B,WAAL,CAAiBsB,MAAjB,GAA0BS,CAA9B,EAAiC;AAC7B,qBAAK/B,WAAL,CAAiB+B,CAAjB,EAAoBxC,IAApB,GAA2BsC,KAAK,CAACE,CAAD,CAAhC;AACH;AACJ;AACJ;;AACD,cAAIzB,IAAI,CAACY,cAAL,CAAoB,iBAApB,KAA0CZ,IAAI,CAAC0B,eAAL,KAAyB,EAAvE,EAA2E;AACvE,iBAAK5B,eAAL,GAAuB;AAAA;AAAA,gCAAMgB,cAAN,CAAqBd,IAAI,CAAC0B,eAA1B,EAA2C,GAA3C,CAAvB;AACH;AACJ;;AACmB,eAAbC,aAAa,CAACC,IAAD,EAAaC,IAAb,EAA2BC,IAA3B,EAAyC;AACzD,cAAMC,SAAS,GAAG,IAAIpD,SAAJ,EAAlB,CADyD,CAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,iBAAOoD,SAAP;AACH;;AAlGkB,O;AAqGvB;AACA;AACA;;;4BACanD,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eACpBK,IADoB,GACL,CADK;AAAA,eAEpB+C,OAFoB,GAEF,CAFE;AAAA,eAGpBC,SAHoB,GAGA,CAHA;AAAA,eAIpBC,QAJoB,GAIC,EAJD;AAAA,eAKpBC,MALoB,GAKD,EALC;AAAA,eAMpBC,WANoB,GAMI,EANJ;AAAA,eAOpBC,cAPoB,GAOO,EAPP;AAAA;;AASpB;AACJ;AACA;AACA;AACItC,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,cAAMsC,KAAK,GAAGtC,IAAI,CAACmB,KAAL,CAAW,GAAX,CAAd;;AACA,cAAImB,KAAK,CAACtB,MAAN,GAAe,CAAnB,EAAsB;AAClB,gBAAMuB,MAAM,GAAG;AAAA;AAAA,gCAAMzB,cAAN,CAAqBwB,KAAK,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAf;AACA,iBAAKN,OAAL,GAAeO,MAAM,CAAC,CAAD,CAArB;AACA,iBAAKN,SAAL,GAAiBM,MAAM,CAAC,CAAD,CAAvB;AACH;;AACD,eAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGa,KAAK,CAACtB,MAA1B,EAAkCS,CAAC,EAAnC,EAAuC;AACnC,gBAAMe,IAAI,GAAGF,KAAK,CAACb,CAAD,CAAlB;;AACA,gBAAIe,IAAI,KAAK,EAAb,EAAiB;AACb,kBAAMC,MAAM,GAAG;AAAA;AAAA,kCAAM3B,cAAN,CAAqB0B,IAArB,EAA2B,GAA3B,CAAf;AACA,mBAAKN,QAAL,CAAcZ,IAAd,CAAmBmB,MAAM,CAAC,CAAD,CAAzB;AACA,mBAAKN,MAAL,CAAYb,IAAZ,CAAiBmB,MAAM,CAAC,CAAD,CAAvB;AACA,mBAAKJ,cAAL,CAAoBf,IAApB,CAAyBmB,MAAM,CAAC,CAAD,CAA/B;AACH;AACJ;AACJ;;AA7BmB,O;AAgCxB;AACA;AACA;;;8BACa5D,Y,GAAN,MAAMA,YAAN,CAAmB;AAAA;AAAA,eACtB6D,MADsB,GACH,EADG;AAAA,eAEtBC,MAFsB,GAEL,CAFK;AAAA;;AAItB;AACJ;AACA;AACA;AACI5C,QAAAA,QAAQ,CAACC,IAAD,EAAqB;AACzB,cAAMsC,KAAK,GAAGtC,IAAI,CAACmB,KAAL,CAAW,GAAX,CAAd;;AACA,cAAImB,KAAK,CAACtB,MAAN,GAAe,CAAnB,EAAsB;AAClB,iBAAK0B,MAAL,GAAc;AAAA;AAAA,gCAAM5B,cAAN,CAAqBwB,KAAK,CAAC,CAAD,CAA1B,EAA+B,GAA/B,CAAd;AACA,iBAAKK,MAAL,GAAcC,QAAQ,CAACN,KAAK,CAAC,CAAD,CAAN,CAAtB;AACH;AACJ;;AAdqB,O", "sourcesContent": ["import { Vec2 } from \"cc\";\r\nimport { ResWave } from \"../../autogen/luban/schema\";\r\nimport { Tools } from \"../utils/Tools\";\r\nimport { Wave } from \"../wave/Wave\";\r\n\r\n/**\r\n * 敌人波次类\r\n */\r\nexport class EnemyWave {\r\n    enemyGroupID: number = 0;\r\n    groupInterval: number = 0;\r\n    type: number = 0;\r\n    enemyID: number = 0;\r\n    enemyInterval: number = 0;\r\n    posDX: number = 0;\r\n    posDY: number = 0;\r\n    enemyNum: number = 0;\r\n    bSetStartPos: boolean = false;\r\n    startPosX: number = 0;\r\n    startPosY: number = 0;\r\n    trackGroups: TrackGroup[] = [];\r\n    liveParam: number[] = [];\r\n    exp: number = 0;\r\n    // normalLoot: WaveLootData | null = null;\r\n    // randomLoot: WaveLootData | null = null;\r\n    rotateSpeed: number = 0;\r\n    firstShootDelay: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载波次信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: ResWave): void {\r\n        this.enemyGroupID = data.enemyGroupID;\r\n        this.groupInterval = data.delay;\r\n        this.type = data.planeType;\r\n        this.enemyID = data.planeId;\r\n        this.enemyInterval = data.interval;\r\n        this.enemyNum = data.num;\r\n        this.rotateSpeed = data.rotatioSpeed;\r\n\r\n\r\n        const point = Tools.stringToPoint(data.offsetPos, \",\");\r\n        this.posDX = point.x;\r\n        this.posDY = point.y;\r\n\r\n        if (data.hasOwnProperty(\"pos\")) {\r\n            const startPos = Tools.stringToNumber(data.pos, \",\");\r\n            if (startPos.length === 2) {\r\n                this.startPosX = startPos[0];\r\n                this.startPosY = startPos[1];\r\n                this.bSetStartPos = true;\r\n            } else {\r\n                this.bSetStartPos = false;\r\n            }\r\n        }\r\n\r\n\r\n        if (data.hasOwnProperty(\"track\")) {\r\n            const ways = data.track.split(\"#\");\r\n            for (const way of ways) {\r\n                if (way !== \"\" && way.split(\";\").length > 1) {\r\n                    const trackGroup = new TrackGroup();\r\n                    trackGroup.loadJson(way);\r\n                    this.trackGroups.push(trackGroup);\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"trackParams\")) {\r\n            const types = Tools.stringToNumber(data.trackParams, \",\");\r\n            for (let i = 0; i < types.length; i++) {\r\n                if (this.trackGroups.length > i) {\r\n                    this.trackGroups[i].type = types[i];\r\n                }\r\n            }\r\n        }\r\n        if (data.hasOwnProperty(\"FirstShootDelay\") && data.FirstShootDelay !== \"\") {\r\n            this.firstShootDelay = Tools.stringToNumber(data.FirstShootDelay, \",\");\r\n        }\r\n    }\r\n    static fromLevelWave(wave: Wave, posX: number, posY: number) {\r\n        const enemyWave = new EnemyWave();\r\n        // enemyWave.enemyGroupID = wave.enemyGroupID;\r\n        // enemyWave.groupInterval = wave.delay;\r\n        // enemyWave.type = wave.planeType;\r\n        // enemyWave.enemyID = wave.planeID;\r\n        // enemyWave.enemyInterval = wave.interval\r\n        // enemyWave.posDX = posX\r\n        // enemyWave.posDY = posY\r\n        // enemyWave.enemyNum = wave.num;\r\n        // enemyWave.bSetStartPos = true\r\n        // enemyWave.startPosX = wave.startPos.x\r\n        // enemyWave.startPosY = wave.startPos.y\r\n        // enemyWave.trackGroups = wave.trackGroups.map(group => {\r\n        //     const trackGroup = new TrackGroup();\r\n        //     trackGroup.loopNum = group.loopNum;\r\n        //     trackGroup.formIndex = group.formIndex;\r\n        //     trackGroup.trackIDs = group.tracks.map(track => track.id);\r\n        //     trackGroup.speeds = group.tracks.map(track => track.speed);\r\n        //     trackGroup.accelerates = group.tracks.map(track => track.accelerate);\r\n        //     trackGroup.trackIntervals = group.tracks.map(track => track.Interval);\r\n        //     trackGroup.type = group.type;\r\n        //     return trackGroup;\r\n        // })\r\n        // enemyWave.firstShootDelay = wave.firstShootDelay;\r\n        return enemyWave;\r\n    }\r\n}\r\n\r\n/**\r\n * 轨迹组类\r\n */\r\nexport class TrackGroup {\r\n    type: number = 0;\r\n    loopNum: number = 0;\r\n    formIndex: number = 0;\r\n    trackIDs: number[] = [];\r\n    speeds: number[] = [];\r\n    accelerates: number[] = [];\r\n    trackIntervals: number[] = [];\r\n\r\n    /**\r\n     * 从 JSON 数据加载轨迹组信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\";\");\r\n        if (parts.length > 0) {\r\n            const header = Tools.stringToNumber(parts[0], \",\");\r\n            this.loopNum = header[0];\r\n            this.formIndex = header[1];\r\n        }\r\n        for (let i = 1; i < parts.length; i++) {\r\n            const part = parts[i];\r\n            if (part !== \"\") {\r\n                const values = Tools.stringToNumber(part, \",\");\r\n                this.trackIDs.push(values[0]);\r\n                this.speeds.push(values[1]);\r\n                this.trackIntervals.push(values[2]);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/**\r\n * 波次掉落数据类\r\n */\r\nexport class WaveLootData {\r\n    enemys: number[] = [];\r\n    lootId: number = 0;\r\n\r\n    /**\r\n     * 从 JSON 数据加载掉落信息\r\n     * @param data JSON 数据\r\n     */\r\n    loadJson(data: string): void {\r\n        const parts = data.split(\";\");\r\n        if (parts.length > 1) {\r\n            this.enemys = Tools.stringToNumber(parts[0], \",\");\r\n            this.lootId = parseInt(parts[1]);\r\n        }\r\n    }\r\n}"]}