System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Tools, EnemyWave, TrackGroup, WaveLootData, _crd;

  function _reportPossibleCrUseOfResWave(extras) {
    _reporterNs.report("ResWave", "../../autogen/luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../wave/Wave", _context.meta, extras);
  }

  _export({
    EnemyWave: void 0,
    TrackGroup: void 0,
    WaveLootData: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bda54n3Pw1F25nIpuoqdv5L", "EnemyWave", undefined);

      __checkObsolete__(['Vec2']);

      /**
       * 敌人波次类
       */
      _export("EnemyWave", EnemyWave = class EnemyWave {
        constructor() {
          this.enemyGroupID = 0;
          this.groupInterval = 0;
          this.type = 0;
          this.enemyID = 0;
          this.enemyInterval = 0;
          this.posDX = 0;
          this.posDY = 0;
          this.enemyNum = 0;
          this.bSetStartPos = false;
          this.startPosX = 0;
          this.startPosY = 0;
          this.trackGroups = [];
          this.liveParam = [];
          this.exp = 0;
          // normalLoot: WaveLootData | null = null;
          // randomLoot: WaveLootData | null = null;
          this.rotateSpeed = 0;
          this.firstShootDelay = [];
        }

        /**
         * 从 JSON 数据加载波次信息
         * @param data JSON 数据
         */
        loadJson(data) {
          this.enemyGroupID = data.enemyGroupID;
          this.groupInterval = data.delay;
          this.type = data.planeType;
          this.enemyID = data.planeId;
          this.enemyInterval = data.interval;
          this.enemyNum = data.num;
          this.rotateSpeed = data.rotatioSpeed;
          const point = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).stringToPoint(data.offsetPos, ",");
          this.posDX = point.x;
          this.posDY = point.y;

          if (data.hasOwnProperty("pos")) {
            const startPos = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.pos, ",");

            if (startPos.length === 2) {
              this.startPosX = startPos[0];
              this.startPosY = startPos[1];
              this.bSetStartPos = true;
            } else {
              this.bSetStartPos = false;
            }
          }

          if (data.hasOwnProperty("track")) {
            const ways = data.track.split("#");

            for (const way of ways) {
              if (way !== "" && way.split(";").length > 1) {
                const trackGroup = new TrackGroup();
                trackGroup.loadJson(way);
                this.trackGroups.push(trackGroup);
              }
            }
          }

          if (data.hasOwnProperty("trackParams")) {
            const types = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.trackParams, ",");

            for (let i = 0; i < types.length; i++) {
              if (this.trackGroups.length > i) {
                this.trackGroups[i].type = types[i];
              }
            }
          }

          if (data.hasOwnProperty("FirstShootDelay") && data.FirstShootDelay !== "") {
            this.firstShootDelay = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(data.FirstShootDelay, ",");
          }
        }

        static fromLevelWave(wave, posX, posY) {
          const enemyWave = new EnemyWave(); // enemyWave.enemyGroupID = wave.enemyGroupID;
          // enemyWave.groupInterval = wave.delay;
          // enemyWave.type = wave.planeType;
          // enemyWave.enemyID = wave.planeID;
          // enemyWave.enemyInterval = wave.interval
          // enemyWave.posDX = posX
          // enemyWave.posDY = posY
          // enemyWave.enemyNum = wave.num;
          // enemyWave.bSetStartPos = true
          // enemyWave.startPosX = wave.startPos.x
          // enemyWave.startPosY = wave.startPos.y
          // enemyWave.trackGroups = wave.trackGroups.map(group => {
          //     const trackGroup = new TrackGroup();
          //     trackGroup.loopNum = group.loopNum;
          //     trackGroup.formIndex = group.formIndex;
          //     trackGroup.trackIDs = group.tracks.map(track => track.id);
          //     trackGroup.speeds = group.tracks.map(track => track.speed);
          //     trackGroup.accelerates = group.tracks.map(track => track.accelerate);
          //     trackGroup.trackIntervals = group.tracks.map(track => track.Interval);
          //     trackGroup.type = group.type;
          //     return trackGroup;
          // })
          // enemyWave.firstShootDelay = wave.firstShootDelay;

          return enemyWave;
        }

      });
      /**
       * 轨迹组类
       */


      _export("TrackGroup", TrackGroup = class TrackGroup {
        constructor() {
          this.type = 0;
          this.loopNum = 0;
          this.formIndex = 0;
          this.trackIDs = [];
          this.speeds = [];
          this.accelerates = [];
          this.trackIntervals = [];
        }

        /**
         * 从 JSON 数据加载轨迹组信息
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = data.split(";");

          if (parts.length > 0) {
            const header = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(parts[0], ",");
            this.loopNum = header[0];
            this.formIndex = header[1];
          }

          for (let i = 1; i < parts.length; i++) {
            const part = parts[i];

            if (part !== "") {
              const values = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).stringToNumber(part, ",");
              this.trackIDs.push(values[0]);
              this.speeds.push(values[1]);
              this.trackIntervals.push(values[2]);
            }
          }
        }

      });
      /**
       * 波次掉落数据类
       */


      _export("WaveLootData", WaveLootData = class WaveLootData {
        constructor() {
          this.enemys = [];
          this.lootId = 0;
        }

        /**
         * 从 JSON 数据加载掉落信息
         * @param data JSON 数据
         */
        loadJson(data) {
          const parts = data.split(";");

          if (parts.length > 1) {
            this.enemys = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).stringToNumber(parts[0], ",");
            this.lootId = parseInt(parts[1]);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c51e5ed53c5c51f600b4d8aaf88c67301186ff63.js.map