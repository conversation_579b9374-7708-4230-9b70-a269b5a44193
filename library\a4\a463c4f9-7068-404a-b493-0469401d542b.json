{"__type__": "sp.SkeletonData", "_name": "skel_deer_skill_bg", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "_skeletonJson": {"skeleton": {"hash": "GUdWA0+AuO5A1qRxZ+PPBWlEDYw", "spine": "3.6.53", "width": 720, "height": 3744.67, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bg", "parent": "root"}, {"name": "bg2", "parent": "bg", "y": 1189.28}, {"name": "bg3", "parent": "bg", "y": 2374.67}], "slots": [{"name": "bg", "bone": "bg", "attachment": "bg"}, {"name": "bg2", "bone": "bg2", "attachment": "bg"}, {"name": "bg3", "bone": "bg3", "attachment": "bg"}], "skins": {"default": {"bg": {"bg": {"y": -152.92, "scaleX": 2, "scaleY": 2, "width": 360, "height": 685}}, "bg2": {"bg": {"y": -152.92, "scaleX": 2, "scaleY": 2, "width": 360, "height": 685}}, "bg3": {"bg": {"y": -152.92, "scaleX": 2, "scaleY": 2, "width": 360, "height": 685}}}}, "animations": {"play": {"slots": {"bg": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.4333, "color": "007affff", "curve": "stepped"}, {"time": 3.8333, "color": "007affff"}, {"time": 4.1667, "color": "007aff00"}], "attachment": [{"time": 0, "name": "bg"}]}, "bg2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.4333, "color": "007affff", "curve": "stepped"}, {"time": 3.8333, "color": "007affff"}, {"time": 4.1667, "color": "007aff00"}], "attachment": [{"time": 0, "name": "bg"}]}, "bg3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.4333, "color": "007affff", "curve": "stepped"}, {"time": 3.8333, "color": "007affff"}, {"time": 4.1667, "color": "007aff00"}], "attachment": [{"time": 0, "name": "bg"}]}}, "bones": {"bg": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -40.75, "y": 199.11, "curve": [0.297, 0.16, 0.637, 0.53]}, {"time": 0.7333, "x": 0, "y": -4138.91, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 746.46, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 1.8333, "x": -33.77, "y": -4138.91, "curve": "stepped"}, {"time": 1.8667, "x": -24.88, "y": 634.5, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 2.7333, "x": 0, "y": -4777.91, "curve": "stepped"}, {"time": 2.7667, "x": -24.88, "y": 634.5, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 3.5333, "x": 0, "y": -4777.91, "curve": "stepped"}, {"time": 3.5667, "x": -24.88, "y": 634.5, "curve": [0.273, 0, 0.62, 0.41]}, {"time": 4.3333, "x": 0, "y": -4777.91}], "scale": [{"time": 0, "x": 1.5, "y": 2}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bg2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bg3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, "textures": [{"__uuid__": "8abf19c1-b4cd-429c-bcf7-ed65a8cae7a1@6c48a", "__expectedType__": "cc.Texture2D"}], "textureNames": ["skel_deer_skill_bg.png"], "scale": 1, "_atlasText": "\nskel_deer_skill_bg.png\nsize: 687,687\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbg\n  rotate: false\n  xy: 1, 1\n  size: 360, 685\n  orig: 360, 685\n  offset: 0, 0\n  index: -1\n"}