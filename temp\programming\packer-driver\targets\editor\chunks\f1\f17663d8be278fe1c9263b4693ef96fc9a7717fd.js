System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Vec2, <PERSON><PERSON>, CCInteger, ExpressionValue, eCompareOp, eConditionOp, eTargetValueType, eWrapMode, eEasing, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _class4, _class5, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _dec18, _dec19, _dec20, _dec21, _class7, _class8, _descriptor12, _descriptor13, _descriptor14, _dec22, _dec23, _dec24, _dec25, _dec26, _dec27, _dec28, _dec29, _dec30, _dec31, _dec32, _dec33, _dec34, _dec35, _dec36, _dec37, _class10, _class11, _descriptor15, _descriptor16, _descriptor17, _descriptor18, _descriptor19, _descriptor20, _descriptor21, _descriptor22, _descriptor23, _descriptor24, _descriptor25, _crd, ccclass, property, eSpawnOrder, eWaveAngleType, eWaveConditionType, eWaveActionType, WaveConditionData, WaveActionData, WaveEventGroupData, WaveData;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfExpressionValue(extras) {
    _reporterNs.report("ExpressionValue", "./bullet/ExpressionValue", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventConditionData(extras) {
    _reporterNs.report("IEventConditionData", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeCompareOp(extras) {
    _reporterNs.report("eCompareOp", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionOp(extras) {
    _reporterNs.report("eConditionOp", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIEventActionData(extras) {
    _reporterNs.report("IEventActionData", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeTargetValueType(extras) {
    _reporterNs.report("eTargetValueType", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeWrapMode(extras) {
    _reporterNs.report("eWrapMode", "./bullet/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../bullet/Easing", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Vec2 = _cc.Vec2;
      Enum = _cc.Enum;
      CCInteger = _cc.CCInteger;
    }, function (_unresolved_2) {
      ExpressionValue = _unresolved_2.ExpressionValue;
    }, function (_unresolved_3) {
      eCompareOp = _unresolved_3.eCompareOp;
      eConditionOp = _unresolved_3.eConditionOp;
      eTargetValueType = _unresolved_3.eTargetValueType;
      eWrapMode = _unresolved_3.eWrapMode;
    }, function (_unresolved_4) {
      eEasing = _unresolved_4.eEasing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "196ac9cYZJPB69dYqcYeFzP", "WaveData", undefined);

      __checkObsolete__(['_decorator', 'error', 'v2', 'Vec2', 'Prefab', 'Enum', 'CCInteger']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("eSpawnOrder", eSpawnOrder = /*#__PURE__*/function (eSpawnOrder) {
        eSpawnOrder[eSpawnOrder["Sequential"] = 0] = "Sequential";
        eSpawnOrder[eSpawnOrder["Random"] = 1] = "Random";
        return eSpawnOrder;
      }({}));

      _export("eWaveAngleType", eWaveAngleType = /*#__PURE__*/function (eWaveAngleType) {
        eWaveAngleType[eWaveAngleType["FacingMoveDir"] = 0] = "FacingMoveDir";
        eWaveAngleType[eWaveAngleType["FacingPlayer"] = 1] = "FacingPlayer";
        eWaveAngleType[eWaveAngleType["Fixed"] = 2] = "Fixed";
        return eWaveAngleType;
      }({}));

      _export("eWaveConditionType", eWaveConditionType = /*#__PURE__*/function (eWaveConditionType) {
        eWaveConditionType[eWaveConditionType["Player_Level"] = 0] = "Player_Level";
        eWaveConditionType[eWaveConditionType["Spawn_Index"] = 1] = "Spawn_Index";
        return eWaveConditionType;
      }({}));

      _export("eWaveActionType", eWaveActionType = /*#__PURE__*/function (eWaveActionType) {
        eWaveActionType[eWaveActionType["Speed"] = 0] = "Speed";
        eWaveActionType[eWaveActionType["SpeedAngle"] = 1] = "SpeedAngle";
        return eWaveActionType;
      }({})); // 和发射器的事件组类似


      _export("WaveConditionData", WaveConditionData = (_dec = ccclass("WaveConditionData"), _dec2 = property({
        type: Enum(_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
          error: Error()
        }), eConditionOp) : eConditionOp),
        displayName: '条件关系'
      }), _dec3 = property({
        visible: false
      }), _dec4 = property({
        type: Enum(_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
          error: Error()
        }), eCompareOp) : eCompareOp),
        displayName: '比较方式'
      }), _dec5 = property({
        visible: false
      }), _dec6 = property({
        displayName: '目标值'
      }), _dec(_class = (_class2 = class WaveConditionData {
        constructor() {
          _initializerDefineProperty(this, "op", _descriptor, this);

          _initializerDefineProperty(this, "type", _descriptor2, this);

          _initializerDefineProperty(this, "compareOp", _descriptor3, this);

          // 条件值: 例如持续时间、距离
          _initializerDefineProperty(this, "targetValue", _descriptor4, this);
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "op", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eConditionOp === void 0 ? (_reportPossibleCrUseOfeConditionOp({
            error: Error()
          }), eConditionOp) : eConditionOp).And;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "type", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eWaveConditionType.Player_Level;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "compareOp", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eCompareOp === void 0 ? (_reportPossibleCrUseOfeCompareOp({
            error: Error()
          }), eCompareOp) : eCompareOp).Equal;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "targetValue", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class2.prototype, "targetValueStr", [_dec6], Object.getOwnPropertyDescriptor(_class2.prototype, "targetValueStr"), _class2.prototype)), _class2)) || _class));

      _export("WaveActionData", WaveActionData = (_dec7 = ccclass("WaveActionData"), _dec8 = property({
        visible: false
      }), _dec9 = property({
        visible: false
      }), _dec10 = property({
        displayName: '持续时间'
      }), _dec11 = property({
        visible: false
      }), _dec12 = property({
        displayName: '目标值'
      }), _dec13 = property({
        type: Enum(_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
          error: Error()
        }), eTargetValueType) : eTargetValueType),
        displayName: '目标值类型'
      }), _dec14 = property({
        visible: false
      }), _dec15 = property({
        displayName: '变换到目标值所需时间'
      }), _dec16 = property({
        type: Enum(_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
          error: Error()
        }), eWrapMode) : eWrapMode),
        displayName: '循环模式'
      }), _dec17 = property({
        type: Enum(_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
          error: Error()
        }), eEasing) : eEasing),
        displayName: '缓动函数'
      }), _dec7(_class4 = (_class5 = class WaveActionData {
        constructor() {
          _initializerDefineProperty(this, "type", _descriptor5, this);

          // 持续时间: 0表示立即执行
          _initializerDefineProperty(this, "duration", _descriptor6, this);

          _initializerDefineProperty(this, "targetValue", _descriptor7, this);

          _initializerDefineProperty(this, "targetValueType", _descriptor8, this);

          _initializerDefineProperty(this, "transitionDuration", _descriptor9, this);

          _initializerDefineProperty(this, "wrapMode", _descriptor10, this);

          _initializerDefineProperty(this, "easing", _descriptor11, this);
        }

        get durationStr() {
          return this.duration.raw;
        }

        set durationStr(value) {
          this.duration.raw = value;
        }

        get targetValueStr() {
          return this.targetValue.raw;
        }

        set targetValueStr(value) {
          this.targetValue.raw = value;
        }

        get transitionDurationStr() {
          return this.transitionDuration.value;
        }

        set transitionDurationStr(value) {
          this.transitionDuration.value = value;
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class5.prototype, "type", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eWaveActionType.Speed;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class5.prototype, "duration", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "durationStr", [_dec10], Object.getOwnPropertyDescriptor(_class5.prototype, "durationStr"), _class5.prototype), _descriptor7 = _applyDecoratedDescriptor(_class5.prototype, "targetValue", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "targetValueStr", [_dec12], Object.getOwnPropertyDescriptor(_class5.prototype, "targetValueStr"), _class5.prototype), _descriptor8 = _applyDecoratedDescriptor(_class5.prototype, "targetValueType", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eTargetValueType === void 0 ? (_reportPossibleCrUseOfeTargetValueType({
            error: Error()
          }), eTargetValueType) : eTargetValueType).Absolute;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class5.prototype, "transitionDuration", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class5.prototype, "transitionDurationStr", [_dec15], Object.getOwnPropertyDescriptor(_class5.prototype, "transitionDurationStr"), _class5.prototype), _descriptor10 = _applyDecoratedDescriptor(_class5.prototype, "wrapMode", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eWrapMode === void 0 ? (_reportPossibleCrUseOfeWrapMode({
            error: Error()
          }), eWrapMode) : eWrapMode).Once;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class5.prototype, "easing", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
            error: Error()
          }), eEasing) : eEasing).Linear;
        }
      })), _class5)) || _class4));

      _export("WaveEventGroupData", WaveEventGroupData = (_dec18 = ccclass("WaveEventGroupData"), _dec19 = property({
        displayName: '事件组名称'
      }), _dec20 = property({
        type: [WaveConditionData],
        displayName: '条件列表'
      }), _dec21 = property({
        type: [WaveActionData],
        displayName: '行为列表'
      }), _dec18(_class7 = (_class8 = class WaveEventGroupData {
        constructor() {
          _initializerDefineProperty(this, "name", _descriptor12, this);

          _initializerDefineProperty(this, "conditions", _descriptor13, this);

          _initializerDefineProperty(this, "actions", _descriptor14, this);
        }

      }, (_descriptor12 = _applyDecoratedDescriptor(_class8.prototype, "name", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return "";
        }
      }), _descriptor13 = _applyDecoratedDescriptor(_class8.prototype, "conditions", [_dec20], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _descriptor14 = _applyDecoratedDescriptor(_class8.prototype, "actions", [_dec21], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class8)) || _class7));
      /**
       * 波次数据：未来代替现有的EnemyWave
       * 所有时间相关的，单位都是毫秒(ms)
       */


      _export("WaveData", WaveData = (_dec22 = ccclass("WaveData"), _dec23 = property({
        visible: false
      }), _dec24 = property({
        displayName: "飞机列表(,号分隔)",
        group: "基础属性"
      }), _dec25 = property({
        type: Enum(eSpawnOrder),
        displayName: "出生顺序",
        group: "基础属性"
      }), _dec26 = property({
        displayName: "出生间隔(ms)",
        group: "基础属性"
      }), _dec27 = property({
        visible: false
      }), _dec28 = property({
        visible: false
      }), _dec29 = property({
        displayName: "出生位置X",
        group: "基础属性"
      }), _dec30 = property({
        displayName: "出生位置Y",
        group: "基础属性"
      }), _dec31 = property({
        visible: false
      }), _dec32 = property({
        displayName: "出生角度",
        group: "基础属性"
      }), _dec33 = property({
        type: CCInteger,
        displayName: "延迟销毁时间",
        tooltip: '单位离开屏幕后, 延迟销毁的时间(ms), -1表示不销毁',
        group: "基础属性"
      }), _dec34 = property({
        type: CCInteger,
        displayName: "出生速度",
        group: "基础属性"
      }), _dec35 = property({
        type: Enum(eWaveAngleType),
        displayName: "单位朝向类型",
        group: "基础属性"
      }), _dec36 = property({
        type: CCInteger,
        // visible() { 
        //     return this.isFixedAngleType;
        // },
        displayName: "单位朝向",
        tooltip: '仅在单位朝向类型为Fixed时有效',
        group: '基础属性'
      }), _dec37 = property({
        type: [WaveEventGroupData],
        displayName: '事件组',
        group: '事件组'
      }), _dec22(_class10 = (_class11 = class WaveData {
        constructor() {
          // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发
          // 因此这里不再配置触发条件
          _initializerDefineProperty(this, "planeList", _descriptor15, this);

          _initializerDefineProperty(this, "spawnOrder", _descriptor16, this);

          _initializerDefineProperty(this, "spawnInterval", _descriptor17, this);

          _initializerDefineProperty(this, "spawnPosX", _descriptor18, this);

          _initializerDefineProperty(this, "spawnPosY", _descriptor19, this);

          this._spawnPos = new Vec2();

          _initializerDefineProperty(this, "spawnAngle", _descriptor20, this);

          _initializerDefineProperty(this, "delayDestroy", _descriptor21, this);

          _initializerDefineProperty(this, "spawnSpeed", _descriptor22, this);

          _initializerDefineProperty(this, "planeAngleType", _descriptor23, this);

          _initializerDefineProperty(this, "planeAngleFixed", _descriptor24, this);

          _initializerDefineProperty(this, "eventGroupData", _descriptor25, this);
        }

        get planeListStr() {
          return (this.planeList || []).join(",");
        }

        set planeListStr(value) {
          if (value === "") {
            this.planeList = [];
            return;
          }

          this.planeList = value.split(",").map(item => parseInt(item));
        }

        get spawnPosXStr() {
          return this.spawnPosX.raw;
        }

        set spawnPosXStr(value) {
          this.spawnPosX.raw = value;
        }

        get spawnPosYStr() {
          return this.spawnPosY.raw;
        }

        set spawnPosYStr(value) {
          this.spawnPosY.raw = value;
        }

        get spawnPos() {
          this._spawnPos.set(this.spawnPosX.eval(), this.spawnPosY.eval());

          return this._spawnPos;
        }

        get spawnAngleStr() {
          return this.spawnAngle.raw;
        }

        set spawnAngleStr(value) {
          this.spawnAngle.raw = value;
        }

        get isFixedAngleType() {
          return this.planeAngleType == eWaveAngleType.Fixed;
        }

      }, (_descriptor15 = _applyDecoratedDescriptor(_class11.prototype, "planeList", [_dec23], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      }), _applyDecoratedDescriptor(_class11.prototype, "planeListStr", [_dec24], Object.getOwnPropertyDescriptor(_class11.prototype, "planeListStr"), _class11.prototype), _descriptor16 = _applyDecoratedDescriptor(_class11.prototype, "spawnOrder", [_dec25], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eSpawnOrder.Sequential;
        }
      }), _descriptor17 = _applyDecoratedDescriptor(_class11.prototype, "spawnInterval", [_dec26], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 1000;
        }
      }), _descriptor18 = _applyDecoratedDescriptor(_class11.prototype, "spawnPosX", [_dec27], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _descriptor19 = _applyDecoratedDescriptor(_class11.prototype, "spawnPosY", [_dec28], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class11.prototype, "spawnPosXStr", [_dec29], Object.getOwnPropertyDescriptor(_class11.prototype, "spawnPosXStr"), _class11.prototype), _applyDecoratedDescriptor(_class11.prototype, "spawnPosYStr", [_dec30], Object.getOwnPropertyDescriptor(_class11.prototype, "spawnPosYStr"), _class11.prototype), _descriptor20 = _applyDecoratedDescriptor(_class11.prototype, "spawnAngle", [_dec31], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return new (_crd && ExpressionValue === void 0 ? (_reportPossibleCrUseOfExpressionValue({
            error: Error()
          }), ExpressionValue) : ExpressionValue)('0');
        }
      }), _applyDecoratedDescriptor(_class11.prototype, "spawnAngleStr", [_dec32], Object.getOwnPropertyDescriptor(_class11.prototype, "spawnAngleStr"), _class11.prototype), _descriptor21 = _applyDecoratedDescriptor(_class11.prototype, "delayDestroy", [_dec33], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 5000;
        }
      }), _descriptor22 = _applyDecoratedDescriptor(_class11.prototype, "spawnSpeed", [_dec34], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 500;
        }
      }), _descriptor23 = _applyDecoratedDescriptor(_class11.prototype, "planeAngleType", [_dec35], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eWaveAngleType.FacingMoveDir;
        }
      }), _descriptor24 = _applyDecoratedDescriptor(_class11.prototype, "planeAngleFixed", [_dec36], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return 0;
        }
      }), _descriptor25 = _applyDecoratedDescriptor(_class11.prototype, "eventGroupData", [_dec37], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return [];
        }
      })), _class11)) || _class10));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f17663d8be278fe1c9263b4693ef96fc9a7717fd.js.map