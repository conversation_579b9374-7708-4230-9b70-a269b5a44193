import { Node, SpriteFrame, Vec2 } from "cc";
import { Tools } from "../utils/Tools";
import { logInfo } from "../../utils/Logger";


export default class GameMapData {

    loadSprite = new Map<string, SpriteFrame>(); // 加载的精灵
    loadImageSque = new Map(); // 加载的图片队列
    speed = 0; // 当前速度
    speeds = []; // 不同层的速度
    layers = []; // 地图层
    PosInfo = []; // 位置信息
    nodeMove = []; // 节点移动信息
    nodeAngle = []; // 节点角度信息
    triggerTime = Vec2.ZERO; // 触发时间范围
    timeInterval = Vec2.ZERO; // 时间间隔范围
    frameTime = 0; // 帧时间
    loopTimes = 0; // 循环次数
    nowTimes = 0; // 当前循环次数
    nowTriggerTime = 0; // 当前触发时间
    tempY = 0; // 临时 Y 坐标
    tempH = 0; // 临时高度
    nowUseNode: Array<Node> = []; // 当前使用的节点
    freeNode = []; // 空闲节点
    index = 0; // 当前索引
    itemIndex = 0; // 项目索引
    spriteName = []; // 精灵名称
    starPos = 0; // 起始位置
    turePosoffSet = 0; // 真实位置偏移
    ViewBot = 0; // 视图底部
    ViewMid = 0; // 视图中间
    ViewTop = 0; // 视图顶部
    scale = 0; // 缩放比例


    /**
     * 清空地图数据
     */
    clear() {
        this.nodeAngle = [];
        this.nodeMove = [];
        this.ViewBot = 0;
        this.ViewTop = 0;
        this.loadSprite.clear();
        this.index = 0;
        this.speed = 0;
        this.nowUseNode.splice(0);
        this.freeNode.splice(0);
        this.itemIndex = 0;
        this.scale = 0;
        this.PosInfo = [];
        this.starPos = 0;
        this.speeds = [];
        this.spriteName = [];
        this.turePosoffSet = 0;
        this.triggerTime = Vec2.ZERO;
        this.timeInterval = Vec2.ZERO;
        this.frameTime = 0;
        this.loopTimes = 0;
        this.nowTimes = 0;
        this.nowTriggerTime = 0;
    }

    /**
     * 获取触发时间
     * @returns {number} 当前触发时间
     */
    getTiggerTime() {
        if (this.nowTriggerTime === 0) {
            this.nowTriggerTime = Math.random() * (this.triggerTime.y - this.triggerTime.x) + this.triggerTime.x;
        }
        return this.nowTriggerTime;
    }

    /**
     * 获取时间间隔
     * @returns {number} 随机时间间隔
     */
    getTimeInterval() {
        return Tools.random_int(this.timeInterval.x, this.timeInterval.y);
    }
}